<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>预约成功 - 图书馆座位预约</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            -webkit-tap-highlight-color: transparent;
            font-family: "PingFang SC", "Helvetica Neue", Arial, sans-serif;
        }

        body {
            background-color: #f8f8f8;
            color: #333;
            font-size: 14px;
            line-height: 1.5;
            overflow-x: hidden;
        }

        /* 模拟iPhone 15 Pro Max */
        .device-container {
            width: 100%;
            max-width: 430px;
            height: 932px;
            margin: 20px auto;
            position: relative;
            overflow: hidden;
            border-radius: 55px;
            box-shadow: 0 20px 50px rgba(0, 0, 0, 0.3);
            background: #1a1a1a;
            padding: 12px;
        }

        /* 刘海区域 */
        .notch {
            position: absolute;
            width: 160px;
            height: 34px;
            background: #1a1a1a;
            top: 12px;
            left: 50%;
            transform: translateX(-50%);
            border-radius: 0 0 20px 20px;
            z-index: 100;
        }

        /* 底部指示条 */
        .home-indicator {
            position: absolute;
            width: 140px;
            height: 5px;
            background: #ffffff;
            bottom: 25px;
            left: 50%;
            transform: translateX(-50%);
            border-radius: 3px;
            z-index: 100;
        }

        /* 手机屏幕 */
        .screen {
            width: 100%;
            height: 100%;
            background: #ffffff;
            border-radius: 45px;
            overflow: hidden;
            position: relative;
        }

        /* 小程序内容区域 */
        .app-container {
            width: 100%;
            height: 100%;
            overflow: hidden;
            display: flex;
            flex-direction: column;
        }

        /* 顶部导航栏 */
        .nav-bar {
            height: 88px;
            background-color: #b1030d;
            color: white;
            display: flex;
            justify-content: space-between;
            align-items: flex-end;
            padding: 0 16px 10px;
            position: relative;
            z-index: 10;
        }

        .nav-left {
            font-size: 24px;
            padding-bottom: 10px;
        }

        .nav-title {
            font-size: 18px;
            font-weight: 600;
            padding-bottom: 10px;
        }

        .nav-right {
            width: 24px;
            padding-bottom: 10px;
        }

        /* 主内容区 */
        .main-content {
            flex: 1;
            overflow-y: auto;
            -webkit-overflow-scrolling: touch;
            padding: 16px;
            padding-bottom: 70px;
        }

        /* 成功提示 */
        .success-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            margin-bottom: 24px;
        }

        .success-icon {
            width: 80px;
            height: 80px;
            border-radius: 40px;
            background: #e8f5e9;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 16px;
            font-size: 40px;
            color: #4CAF50;
            animation: scaleIn 0.5s ease forwards;
        }

        .success-title {
            font-size: 22px;
            font-weight: 600;
            color: #333;
            margin-bottom: 8px;
            animation: fadeIn 0.5s ease forwards;
            animation-delay: 0.2s;
            opacity: 0;
        }

        .success-desc {
            font-size: 14px;
            color: #666;
            text-align: center;
            animation: fadeIn 0.5s ease forwards;
            animation-delay: 0.3s;
            opacity: 0;
        }

        /* 预约信息卡片 */
        .booking-card {
            background: white;
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            animation: slideUp 0.5s ease forwards;
            animation-delay: 0.4s;
            opacity: 0;
            transform: translateY(20px);
        }

        .booking-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
            padding-bottom: 16px;
            border-bottom: 1px solid #f0f0f0;
        }

        .booking-title {
            font-size: 18px;
            font-weight: 600;
            color: #333;
        }

        .booking-id {
            font-size: 13px;
            color: #999;
        }

        .booking-info-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 16px;
        }

        .booking-info-item {
            display: flex;
            flex-direction: column;
        }

        .info-label {
            font-size: 13px;
            color: #999;
            margin-bottom: 4px;
        }

        .info-value {
            font-size: 16px;
            color: #333;
            font-weight: 500;
        }

        .info-value.highlight {
            color: #b1030d;
            font-weight: 600;
        }

        /* 二维码区域 */
        .qrcode-container {
            background: white;
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            display: flex;
            flex-direction: column;
            align-items: center;
            animation: slideUp 0.5s ease forwards;
            animation-delay: 0.5s;
            opacity: 0;
            transform: translateY(20px);
        }

        .qrcode-title {
            font-size: 16px;
            font-weight: 600;
            color: #333;
            margin-bottom: 16px;
        }

        .qrcode-image {
            width: 200px;
            height: 200px;
            background: white;
            border: 1px solid #eee;
            border-radius: 8px;
            padding: 8px;
            margin-bottom: 16px;
            position: relative;
        }

        .qrcode-image img {
            width: 100%;
            height: 100%;
            object-fit: contain;
        }

        .qrcode-logo {
            position: absolute;
            width: 40px;
            height: 40px;
            background: white;
            border-radius: 20px;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .qrcode-logo-inner {
            width: 30px;
            height: 30px;
            background: #b1030d;
            border-radius: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 16px;
            font-weight: bold;
        }

        .qrcode-desc {
            font-size: 13px;
            color: #666;
            text-align: center;
            max-width: 280px;
        }

        /* 操作按钮 */
        .action-buttons {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 12px;
            margin-bottom: 20px;
            animation: slideUp 0.5s ease forwards;
            animation-delay: 0.6s;
            opacity: 0;
            transform: translateY(20px);
        }

        .action-button {
            height: 50px;
            border-radius: 25px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 15px;
            font-weight: 600;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
        }

        .primary-btn {
            background: #b1030d;
            color: white;
        }

        .secondary-btn {
            background: white;
            color: #b1030d;
            border: 1px solid #b1030d;
        }

        .action-button i {
            margin-right: 6px;
        }

        /* 底部导航 */
        .tab-bar {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: white;
            display: flex;
            box-shadow: 0 -1px 10px rgba(0, 0, 0, 0.05);
            z-index: 100;
        }

        .tab-item {
            flex: 1;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            color: #999;
            font-size: 10px;
            transition: all 0.3s ease;
        }

        .tab-item.active {
            color: #b1030d;
        }

        .tab-icon {
            font-size: 24px;
            margin-bottom: 2px;
        }

        /* 图标字体 */
        .icon {
            font-family: "Material Icons";
            font-weight: normal;
            font-style: normal;
            font-size: 24px;
            display: inline-block;
            line-height: 1;
            text-transform: none;
            letter-spacing: normal;
            word-wrap: normal;
            white-space: nowrap;
            direction: ltr;
            -webkit-font-smoothing: antialiased;
            text-rendering: optimizeLegibility;
            -moz-osx-font-smoothing: grayscale;
            font-feature-settings: 'liga';
        }

        /* 动画效果 */
        @keyframes fadeIn {
            from {
                opacity: 0;
            }
            to {
                opacity: 1;
            }
        }

        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes scaleIn {
            0% {
                transform: scale(0);
                opacity: 0;
            }
            50% {
                transform: scale(1.2);
                opacity: 1;
            }
            100% {
                transform: scale(1);
                opacity: 1;
            }
        }

        /* 分享按钮 */
        .share-button {
            position: absolute;
            top: 16px;
            right: 16px;
            width: 36px;
            height: 36px;
            border-radius: 18px;
            background: rgba(255, 255, 255, 0.9);
            display: flex;
            align-items: center;
            justify-content: center;
            color: #666;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            z-index: 10;
        }

        /* 倒计时提示 */
        .countdown-tip {
            background: #fff8e1;
            border-radius: 12px;
            padding: 12px 16px;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            animation: slideUp 0.5s ease forwards;
            animation-delay: 0.7s;
            opacity: 0;
            transform: translateY(20px);
        }

        .countdown-tip i {
            color: #FFC107;
            margin-right: 8px;
            font-size: 20px;
        }

        .countdown-tip-text {
            font-size: 13px;
            color: #333;
        }

        .countdown-time {
            font-weight: 600;
            color: #b1030d;
        }
    </style>
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
</head>
<body>
    <div class="device-container">
        <div class="notch"></div>
        <div class="home-indicator"></div>
        <div class="screen">
            <div class="app-container">
                <!-- 顶部导航栏 -->
                <div class="nav-bar">
                    <div class="nav-left">
                        <i class="icon" onclick="goBack()">arrow_back</i>
                    </div>
                    <div class="nav-title">预约成功</div>
                    <div class="nav-right"></div>
                </div>

                <!-- 主内容区 -->
                <div class="main-content">
                    <!-- 成功提示 -->
                    <div class="success-container">
                        <div class="success-icon">
                            <i class="icon">check_circle</i>
                        </div>
                        <div class="success-title">预约成功</div>
                        <div class="success-desc">您已成功预约图书馆座位<br>请按时到达并完成签到</div>
                    </div>

                    <!-- 预约信息卡片 -->
                    <div class="booking-card">
                        <div class="booking-header">
                            <div class="booking-title">A101 自习室</div>
                            <div class="booking-id">预约号: 2023051701</div>
                        </div>
                        <div class="booking-info-grid">
                            <div class="booking-info-item">
                                <div class="info-label">座位号</div>
                                <div class="info-value highlight">A5</div>
                            </div>
                            <div class="booking-info-item">
                                <div class="info-label">日期</div>
                                <div class="info-value">2023年5月17日</div>
                            </div>
                            <div class="booking-info-item">
                                <div class="info-label">时间段</div>
                                <div class="info-value">12:00 - 17:00，12:00 - 17:00</div>
                            </div>
                            <div class="booking-info-item">
                                <div class="info-label">位置</div>
                                <div class="info-value">图书馆1楼</div>
                            </div>
                        </div>
                    </div>

                    <!-- 倒计时提示 -->
                    <div class="countdown-tip">
                        <i class="icon">schedule</i>
                        <div class="countdown-tip-text">
                            请在 <span class="countdown-time">12:15</span> 前完成签到，否则预约将被取消
                        </div>
                    </div>

                    <!-- 二维码区域 -->
                   

                    <!-- 操作按钮 -->
                    <div class="action-buttons">
                        <div class="action-button secondary-btn" onclick="goToHome()">
                            <i class="icon">home</i> 返回首页
                        </div>
                        <div class="action-button primary-btn" onclick="goToMyBookings()">
                            <i class="icon">list</i> 我的预约
                        </div>
                    </div>

                    <!-- 分享按钮 -->
                    <div class="share-button" onclick="shareBooking()">
                        <i class="icon">share</i>
                    </div>
                </div>

                <!-- 底部导航栏 -->
                <div class="tab-bar">
                    <div class="tab-item">
                        <i class="icon tab-icon">event_seat</i>
                        <div>预约</div>
                    </div>
                    <div class="tab-item active">
                        <i class="icon tab-icon">schedule</i>
                        <div>我的座位</div>
                    </div>
                    <div class="tab-item">
                        <i class="icon tab-icon">person</i>
                        <div>我的</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 返回上一页
        function goBack() {
            // 实际应用中应该使用微信小程序的导航API
            alert('返回上一页');
            // wx.navigateBack();
        }

        // 前往首页
        function goToHome() {
            // 实际应用中应该使用微信小程序的导航API
            alert('前往首页');
            // wx.switchTab({url: '/pages/index/index'});
        }

        // 前往我的预约列表
        function goToMyBookings() {
            // 实际应用中应该使用微信小程序的导航API
            alert('前往我的预约列表');
            // wx.navigateTo({url: '/pages/my-bookings/index'});
        }

        // 分享预约
        function shareBooking() {
            // 实际应用中应该使用微信小程序的分享API
            alert('分享预约信息');
            // wx.showShareMenu();
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 可以添加一些初始化逻辑
        });
    </script>
</body>
</html>
