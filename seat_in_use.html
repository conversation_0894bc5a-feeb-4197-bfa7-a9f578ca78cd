<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>座位使用中 - 图书馆座位预约</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            -webkit-tap-highlight-color: transparent;
            font-family: "PingFang SC", "Helvetica Neue", Arial, sans-serif;
        }

        body {
            background-color: #f8f8f8;
            color: #333;
            font-size: 14px;
            line-height: 1.5;
            overflow-x: hidden;
        }

        /* 模拟iPhone 15 Pro Max */
        .device-container {
            width: 100%;
            max-width: 430px;
            height: 932px;
            margin: 20px auto;
            position: relative;
            overflow: hidden;
            border-radius: 55px;
            box-shadow: 0 20px 50px rgba(0, 0, 0, 0.3);
            background: #1a1a1a;
            padding: 12px;
        }

        /* 刘海区域 */
        .notch {
            position: absolute;
            width: 160px;
            height: 34px;
            background: #1a1a1a;
            top: 12px;
            left: 50%;
            transform: translateX(-50%);
            border-radius: 0 0 20px 20px;
            z-index: 100;
        }

        /* 底部指示条 */
        .home-indicator {
            position: absolute;
            width: 140px;
            height: 5px;
            background: #ffffff;
            bottom: 25px;
            left: 50%;
            transform: translateX(-50%);
            border-radius: 3px;
            z-index: 100;
        }

        /* 手机屏幕 */
        .screen {
            width: 100%;
            height: 100%;
            background: #ffffff;
            border-radius: 45px;
            overflow: hidden;
            position: relative;
        }

        /* 小程序内容区域 */
        .app-container {
            width: 100%;
            height: 100%;
            overflow: hidden;
            display: flex;
            flex-direction: column;
        }

        /* 顶部导航栏 */
        .nav-bar {
            height: 88px;
            background-color: #b1030d;
            color: white;
            display: flex;
            justify-content: space-between;
            align-items: flex-end;
            padding: 0 16px 10px;
            position: relative;
            z-index: 10;
        }

        .nav-left {
            width: 24px;
            padding-bottom: 10px;
        }

        .nav-title {
            font-size: 18px;
            font-weight: 600;
            padding-bottom: 10px;
        }

        .nav-right {
            width: 24px;
            padding-bottom: 10px;
        }

        /* 主内容区 */
        .main-content {
            flex: 1;
            overflow-y: auto;
            -webkit-overflow-scrolling: touch;
            padding: 16px;
            padding-bottom: 70px;
        }

        /* 状态卡片 */
        .status-card {
            background: white;
            border-radius: 20px;
            padding: 24px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            text-align: center;
            position: relative;
        }

        .status-title {
            font-size: 16px;
            color: #666;
            margin-bottom: 20px;
        }

        /* 环形进度条 */
        .progress-ring-container {
            position: relative;
            width: 180px;
            height: 180px;
            margin: 0 auto 20px;
        }

        .progress-ring {
            transform: rotate(-90deg);
            width: 100%;
            height: 100%;
        }

        .progress-ring-circle {
            fill: transparent;
            stroke: #f0f0f0;
            stroke-width: 8;
        }

        .progress-ring-circle-progress {
            fill: transparent;
            stroke: #4CAF50;
            stroke-width: 8;
            stroke-linecap: round;
            transition: stroke-dashoffset 0.3s ease;
        }

        .progress-ring-circle-progress.temp-leave {
            stroke: #FFC107;
        }

        .progress-ring-text {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            text-align: center;
        }

        .remaining-time {
            font-size: 28px;
            font-weight: 600;
            color: #333;
            margin-bottom: 4px;
        }

        .time-label {
            font-size: 14px;
            color: #666;
        }

        .status-badge {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 12px;
            font-size: 14px;
            font-weight: 500;
            margin-bottom: 8px;
        }

        .status-badge.using {
            background: #e8f5e9;
            color: #4CAF50;
        }

        .status-badge.temp-leave {
            background: #fff8e1;
            color: #FFC107;
        }

        /* 座位信息卡片 */
        .seat-info-card {
            background: white;
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
        }

        .seat-info-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }

        .seat-info-title {
            font-size: 18px;
            font-weight: 600;
            color: #333;
        }

        .seat-number {
            font-size: 16px;
            font-weight: 600;
            color: #b1030d;
        }

        .seat-info-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 12px;
        }

        .seat-info-item {
            display: flex;
            flex-direction: column;
        }

        .seat-info-label {
            font-size: 13px;
            color: #999;
            margin-bottom: 4px;
        }

        .seat-info-value {
            font-size: 15px;
            color: #333;
        }

        /* 使用统计卡片 */
        .usage-stats-card {
            background: white;
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
        }

        .usage-stats-title {
            font-size: 16px;
            font-weight: 600;
            color: #333;
            margin-bottom: 16px;
        }

        .usage-stats-grid {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 12px;
        }

        .usage-stat-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            text-align: center;
        }

        .usage-stat-value {
            font-size: 20px;
            font-weight: 600;
            color: #333;
            margin-bottom: 4px;
        }

        .usage-stat-label {
            font-size: 12px;
            color: #999;
        }

        /* 操作按钮 */
        .action-buttons {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 12px;
            margin-bottom: 20px;
        }

        .action-button {
            height: 56px;
            border-radius: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
            font-weight: 600;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
        }

        .temp-leave-btn {
            background: #fff8e1;
            color: #FFC107;
            border: 1px solid #FFC107;
        }

        .return-btn {
            background: #e8f5e9;
            color: #4CAF50;
            border: 1px solid #4CAF50;
        }

        .check-out-btn {
            background: #ffebee;
            color: #b1030d;
            border: 1px solid #b1030d;
        }

        .action-button i {
            margin-right: 8px;
        }

        /* 底部导航 */
        .tab-bar {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: white;
            display: flex;
            box-shadow: 0 -1px 10px rgba(0, 0, 0, 0.05);
            z-index: 100;
        }

        .tab-item {
            flex: 1;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            color: #999;
            font-size: 10px;
            transition: all 0.3s ease;
        }

        .tab-item.active {
            color: #b1030d;
        }

        .tab-icon {
            font-size: 24px;
            margin-bottom: 2px;
        }

        /* 图标字体 */
        .icon {
            font-family: "Material Icons";
            font-weight: normal;
            font-style: normal;
            font-size: 24px;
            display: inline-block;
            line-height: 1;
            text-transform: none;
            letter-spacing: normal;
            word-wrap: normal;
            white-space: nowrap;
            direction: ltr;
            -webkit-font-smoothing: antialiased;
            text-rendering: optimizeLegibility;
            -moz-osx-font-smoothing: grayscale;
            font-feature-settings: 'liga';
        }

        /* 动画效果 */
        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .animate-in {
            animation: fadeIn 0.5s ease forwards;
        }

        /* 模态框 */
        .modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1000;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
        }

        .modal.show {
            opacity: 1;
            visibility: visible;
        }

        .modal-content {
            width: 80%;
            max-width: 320px;
            background: white;
            border-radius: 16px;
            padding: 24px;
            text-align: center;
            transform: translateY(20px);
            transition: all 0.3s ease;
        }

        .modal.show .modal-content {
            transform: translateY(0);
        }

        .modal-icon {
            width: 60px;
            height: 60px;
            border-radius: 30px;
            background: #e8f5e9;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 16px;
            font-size: 30px;
            color: #4CAF50;
        }

        .modal-title {
            font-size: 18px;
            font-weight: 600;
            color: #333;
            margin-bottom: 8px;
        }

        .modal-desc {
            font-size: 14px;
            color: #666;
            margin-bottom: 20px;
        }

        .modal-buttons {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 12px;
        }

        .modal-button {
            height: 44px;
            border-radius: 22px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
            font-weight: 600;
        }

        .modal-button-cancel {
            background: #f5f5f5;
            color: #666;
        }

        .modal-button-confirm {
            background: #b1030d;
            color: white;
        }

        .modal-button-single {
            grid-column: span 2;
        }
    </style>
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
</head>
<body>
    <div class="device-container">
        <div class="notch"></div>
        <div class="home-indicator"></div>
        <div class="screen">
            <div class="app-container">
                <!-- 顶部导航栏 -->
                <div class="nav-bar">
                    <div class="nav-left"></div>
                    <div class="nav-title">我的座位</div>
                    <div class="nav-right"></div>
                </div>

                <!-- 主内容区 -->
                <div class="main-content">
                    <!-- 状态卡片 -->
                    <div class="status-card animate-in" id="statusCard">
                        <div class="status-badge using" id="statusBadge">使用中</div>
                        <div class="status-title">剩余使用时间</div>
                        
                        <div class="progress-ring-container">
                            <svg class="progress-ring" width="180" height="180">
                                <circle class="progress-ring-circle" cx="90" cy="90" r="80"></circle>
                                <circle class="progress-ring-circle-progress" id="progressRing" cx="90" cy="90" r="80"></circle>
                            </svg>
                            <div class="progress-ring-text">
                                <div class="remaining-time" id="remainingTime">3:45</div>
                                <div class="time-label">小时</div>
                            </div>
                        </div>
                    </div>

                    <!-- 座位信息卡片 -->
                    <div class="seat-info-card animate-in" style="animation-delay: 0.1s;">
                        <div class="seat-info-header">
                            <div class="seat-info-title">A101 自习室</div>
                            <div class="seat-number">A5</div>
                        </div>
                        <div class="seat-info-grid">
                            <div class="seat-info-item">
                                <div class="seat-info-label">日期</div>
                                <div class="seat-info-value">2023年5月17日</div>
                            </div>
                            <div class="seat-info-item">
                                <div class="seat-info-label">时间段</div>
                                <div class="seat-info-value">12:00 - 17:00</div>
                            </div>
                            <div class="seat-info-item">
                                <div class="seat-info-label">签到时间</div>
                                <div class="seat-info-value">12:05</div>
                            </div>
                            <div class="seat-info-item">
                                <div class="seat-info-label">位置</div>
                                <div class="seat-info-value">图书馆1楼</div>
                            </div>
                        </div>
                    </div>

                    <!-- 使用统计卡片 -->
                    <div class="usage-stats-card animate-in" style="animation-delay: 0.2s;">
                        <div class="usage-stats-title">使用统计</div>
                        <div class="usage-stats-grid">
                            <div class="usage-stat-item">
                                <div class="usage-stat-value" id="usedTime">1:15</div>
                                <div class="usage-stat-label">已使用</div>
                            </div>
                            <div class="usage-stat-item">
                                <div class="usage-stat-value" id="tempLeaveCount">0</div>
                                <div class="usage-stat-label">暂离次数</div>
                            </div>
                            <div class="usage-stat-item">
                                <div class="usage-stat-value" id="tempLeaveTime">0</div>
                                <div class="usage-stat-label">暂离时间</div>
                            </div>
                        </div>
                    </div>

                    <!-- 操作按钮 -->
                    <div class="action-buttons animate-in" style="animation-delay: 0.3s;" id="actionButtons">
                        <div class="action-button temp-leave-btn" id="tempLeaveBtn" onclick="tempLeave()">
                            <i class="icon">timer</i> 暂离
                        </div>
                        <div class="action-button check-out-btn" onclick="checkOut()">
                            <i class="icon">logout</i> 签退
                        </div>
                    </div>
                </div>

                <!-- 底部导航栏 -->
                <div class="tab-bar">
                    <div class="tab-item">
                        <i class="icon tab-icon">event_seat</i>
                        <div>预约</div>
                    </div>
                    <div class="tab-item active">
                        <i class="icon tab-icon">schedule</i>
                        <div>我的座位</div>
                    </div>
                    <div class="tab-item">
                        <i class="icon tab-icon">person</i>
                        <div>我的</div>
                    </div>
                </div>

                <!-- 暂离确认模态框 -->
                <div class="modal" id="tempLeaveModal">
                    <div class="modal-content">
                        <div class="modal-icon" style="background: #fff8e1; color: #FFC107;">
                            <i class="icon">timer</i>
                        </div>
                        <div class="modal-title">确认暂离</div>
                        <div class="modal-desc">暂离时间不得超过30分钟<br>超时未返回座位将被释放</div>
                        <div class="modal-buttons">
                            <div class="modal-button modal-button-cancel" onclick="closeModal()">
                                取消
                            </div>
                            <div class="modal-button modal-button-confirm" onclick="confirmTempLeave()">
                                确认
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 签退确认模态框 -->
                <div class="modal" id="checkOutModal">
                    <div class="modal-content">
                        <div class="modal-icon" style="background: #ffebee; color: #b1030d;">
                            <i class="icon">logout</i>
                        </div>
                        <div class="modal-title">确认签退</div>
                        <div class="modal-desc">签退后座位将被释放<br>确定要结束本次使用吗？</div>
                        <div class="modal-buttons">
                            <div class="modal-button modal-button-cancel" onclick="closeModal()">
                                取消
                            </div>
                            <div class="modal-button modal-button-confirm" onclick="confirmCheckOut()">
                                确认
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 暂离返回确认模态框 -->
                <div class="modal" id="returnModal">
                    <div class="modal-content">
                        <div class="modal-icon" style="background: #e8f5e9; color: #4CAF50;">
                            <i class="icon">check_circle</i>
                        </div>
                        <div class="modal-title">确认返回</div>
                        <div class="modal-desc">您是否已返回座位？<br>请确认后继续使用</div>
                        <div class="modal-button modal-button-confirm modal-button-single" onclick="confirmReturn()">
                            确认返回
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 当前状态
        let currentStatus = 'using'; // using, temp_leave
        let totalTimeMinutes = 5 * 60; // 总时间5小时
        let remainingTimeMinutes = 3 * 60 + 45; // 剩余时间3小时45分钟
        let usedTimeMinutes = totalTimeMinutes - remainingTimeMinutes; // 已使用时间
        let tempLeaveCount = 0; // 暂离次数
        let tempLeaveTimeMinutes = 0; // 暂离总时间
        let countdownInterval;

        // 初始化环形进度条
        function initProgressRing() {
            const circle = document.getElementById('progressRing');
            const radius = circle.r.baseVal.value;
            const circumference = 2 * Math.PI * radius;
            
            // 设置初始周长
            circle.style.strokeDasharray = `${circumference} ${circumference}`;
            circle.style.strokeDashoffset = circumference;
            
            // 更新进度
            updateProgress(remainingTimeMinutes / totalTimeMinutes);
        }

        // 更新进度条
        function updateProgress(percent) {
            const circle = document.getElementById('progressRing');
            const radius = circle.r.baseVal.value;
            const circumference = 2 * Math.PI * radius;
            
            const offset = circumference - percent * circumference;
            circle.style.strokeDashoffset = offset;
        }

        // 更新剩余时间显示
        function updateRemainingTimeDisplay() {
            const hours = Math.floor(remainingTimeMinutes / 60);
            const minutes = remainingTimeMinutes % 60;
            
            document.getElementById('remainingTime').textContent = `${hours}:${minutes.toString().padStart(2, '0')}`;
            
            // 如果剩余时间小于30分钟，添加警示效果
            if (remainingTimeMinutes < 30) {
                document.getElementById('remainingTime').style.color = '#b1030d';
            } else {
                document.getElementById('remainingTime').style.color = '';
            }
        }

        // 更新已使用时间显示
        function updateUsedTimeDisplay() {
            const hours = Math.floor(usedTimeMinutes / 60);
            const minutes = usedTimeMinutes % 60;
            
            document.getElementById('usedTime').textContent = `${hours}:${minutes.toString().padStart(2, '0')}`;
        }

        // 更新暂离时间显示
        function updateTempLeaveTimeDisplay() {
            const minutes = tempLeaveTimeMinutes;
            document.getElementById('tempLeaveTime').textContent = `${minutes}分钟`;
        }

        // 开始倒计时
        function startCountdown() {
            // 清除之前的倒计时
            if (countdownInterval) {
                clearInterval(countdownInterval);
            }
            
            // 设置倒计时间隔
            countdownInterval = setInterval(() => {
                if (currentStatus === 'using') {
                    // 使用中状态，剩余时间减少，已使用时间增加
                    remainingTimeMinutes--;
                    usedTimeMinutes++;
                    
                    if (remainingTimeMinutes <= 0) {
                        clearInterval(countdownInterval);
                        alert('使用时间已到，请签退');
                    }
                    
                    // 更新显示
                    updateRemainingTimeDisplay();
                    updateUsedTimeDisplay();
                    updateProgress(remainingTimeMinutes / totalTimeMinutes);
                } else if (currentStatus === 'temp_leave') {
                    // 暂离状态，剩余暂离时间减少，暂离总时间增加
                    remainingTimeMinutes--;
                    tempLeaveTimeMinutes++;
                    
                    if (remainingTimeMinutes <= 0) {
                        clearInterval(countdownInterval);
                        alert('暂离时间已到，座位已被释放');
                        // 实际应用中应该跳转到预约列表页面
                    }
                    
                    // 更新显示
                    updateRemainingTimeDisplay();
                    updateTempLeaveTimeDisplay();
                    updateProgress(remainingTimeMinutes / 30); // 暂离最长30分钟
                }
            }, 1000);
        }

        // 暂离
        function tempLeave() {
            document.getElementById('tempLeaveModal').classList.add('show');
        }

        // 确认暂离
        function confirmTempLeave() {
            // 关闭模态框
            closeModal();
            
            // 更新状态为暂离
            currentStatus = 'temp_leave';
            
            // 更新UI
            document.getElementById('statusBadge').textContent = '暂离中';
            document.getElementById('statusBadge').className = 'status-badge temp-leave';
            document.getElementById('progressRing').classList.add('temp-leave');
            
            // 更新按钮
            document.getElementById('actionButtons').innerHTML = `
                <div class="action-button return-btn" onclick="returnFromTempLeave()">
                    <i class="icon">keyboard_return</i> 返回
                </div>
                <div class="action-button check-out-btn" onclick="checkOut()">
                    <i class="icon">logout</i> 签退
                </div>
            `;
            
            // 增加暂离次数
            tempLeaveCount++;
            document.getElementById('tempLeaveCount').textContent = tempLeaveCount;
            
            // 重置剩余时间为30分钟（暂离最长时间）
            remainingTimeMinutes = 30;
            updateRemainingTimeDisplay();
            updateProgress(1); // 重置进度条
            
            // 重新开始倒计时
            startCountdown();
        }

        // 从暂离返回
        function returnFromTempLeave() {
            document.getElementById('returnModal').classList.add('show');
        }

        // 确认返回
        function confirmReturn() {
            // 关闭模态框
            closeModal();
            
            // 更新状态为使用中
            currentStatus = 'using';
            
            // 更新UI
            document.getElementById('statusBadge').textContent = '使用中';
            document.getElementById('statusBadge').className = 'status-badge using';
            document.getElementById('progressRing').classList.remove('temp-leave');
            
            // 更新按钮
            document.getElementById('actionButtons').innerHTML = `
                <div class="action-button temp-leave-btn" onclick="tempLeave()">
                    <i class="icon">timer</i> 暂离
                </div>
                <div class="action-button check-out-btn" onclick="checkOut()">
                    <i class="icon">logout</i> 签退
                </div>
            `;
            
            // 恢复剩余使用时间（这里简化处理，实际应用中应该从服务器获取）
            remainingTimeMinutes = 3 * 60 + 15; // 假设返回后剩余3小时15分钟
            updateRemainingTimeDisplay();
            updateProgress(remainingTimeMinutes / totalTimeMinutes);
            
            // 重新开始倒计时
            startCountdown();
        }

        // 签退
        function checkOut() {
            document.getElementById('checkOutModal').classList.add('show');
        }

        // 确认签退
        function confirmCheckOut() {
            // 关闭模态框
            closeModal();
            
            // 模拟返回预约列表
            alert('签退成功，即将返回预约列表');
            // 实际应用中应该使用微信小程序的导航API
            // wx.navigateBack();
        }

        // 关闭模态框
        function closeModal() {
            document.getElementById('tempLeaveModal').classList.remove('show');
            document.getElementById('checkOutModal').classList.remove('show');
            document.getElementById('returnModal').classList.remove('show');
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            initProgressRing();
            updateRemainingTimeDisplay();
            updateUsedTimeDisplay();
            document.getElementById('tempLeaveCount').textContent = tempLeaveCount;
            document.getElementById('tempLeaveTime').textContent = `${tempLeaveTimeMinutes}分钟`;
            
            // 开始倒计时
            startCountdown();
        });
    </script>
</body>
</html>
