<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>座位管理 - 图书馆座位预约系统</title>
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: "PingFang SC", "Helvetica Neue", Arial, sans-serif;
        }

        body {
            background-color: #f5f5f5;
            color: #333;
            font-size: 14px;
            line-height: 1.5;
            display: flex;
            min-height: 100vh;
        }

        /* 侧边栏样式 */
        .sidebar {
            width: 240px;
            background: #2c3e50;
            color: white;
            height: 100vh;
            position: fixed;
            left: 0;
            top: 0;
            overflow-y: auto;
        }

        .sidebar-header {
            padding: 20px;
            text-align: center;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .sidebar-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 5px;
        }

        .sidebar-subtitle {
            font-size: 12px;
            opacity: 0.7;
        }

        .sidebar-menu {
            padding: 20px 0;
        }

        .menu-item {
            padding: 12px 20px;
            display: flex;
            align-items: center;
            color: rgba(255, 255, 255, 0.7);
            text-decoration: none;
            transition: all 0.3s;
        }

        .menu-item:hover {
            background: rgba(255, 255, 255, 0.1);
            color: white;
        }

        .menu-item.active {
            background: #b1030d;
            color: white;
        }

        .menu-icon {
            margin-right: 10px;
            font-size: 20px;
        }

        .menu-text {
            font-size: 14px;
        }

        /* 主内容区样式 */
        .main-content {
            flex: 1;
            margin-left: 240px;
            padding: 20px;
        }

        /* 顶部导航栏 */
        .top-nav {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 20px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            margin-bottom: 20px;
        }

        .page-title {
            font-size: 20px;
            font-weight: 600;
            color: #333;
        }

        .user-info {
            display: flex;
            align-items: center;
        }

        .user-name {
            margin-right: 10px;
            font-weight: 500;
        }

        .user-avatar {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: #b1030d;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
        }

        /* 筛选和操作栏 */
        .filter-bar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 20px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            margin-bottom: 20px;
        }

        .filter-group {
            display: flex;
            align-items: center;
        }

        .filter-label {
            margin-right: 10px;
            font-weight: 500;
        }

        .filter-select {
            height: 36px;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 0 10px;
            margin-right: 15px;
        }

        .action-button {
            height: 36px;
            padding: 0 15px;
            background: #b1030d;
            color: white;
            border: none;
            border-radius: 4px;
            font-weight: 500;
            cursor: pointer;
            display: flex;
            align-items: center;
        }

        .action-button .material-icons {
            margin-right: 5px;
            font-size: 18px;
        }

        /* 座位布局卡片 */
        .seat-layout-card {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            margin-bottom: 20px;
            overflow: hidden;
        }

        .card-header {
            padding: 15px 20px;
            border-bottom: 1px solid #f0f0f0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .card-title {
            font-size: 16px;
            font-weight: 600;
        }

        .card-actions {
            display: flex;
        }

        .card-action {
            margin-left: 10px;
            color: #777;
            cursor: pointer;
        }

        .card-action:hover {
            color: #b1030d;
        }

        .card-content {
            padding: 20px;
        }

        /* 座位布局 */
        .room-layout {
            display: flex;
            flex-direction: column;
            align-items: center;
        }

        .entrance {
            width: 120px;
            height: 30px;
            background: #eacdcf;
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            color: #b1030d;
            margin-bottom: 20px;
        }

        .seat-grid {
            display: grid;
            grid-template-columns: repeat(8, 1fr);
            gap: 10px;
            width: 100%;
            max-width: 800px;
        }

        .seat {
            width: 40px;
            height: 40px;
            border-radius: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: 600;
            color: white;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .seat-available {
            background-color: #4CAF50;
        }

        .seat-occupied {
            background-color: #9e9e9e;
        }

        .seat-selected {
            background-color: #b1030d;
            transform: scale(1.05);
            box-shadow: 0 2px 8px rgba(177, 3, 13, 0.3);
        }

        .seat-empty {
            visibility: hidden;
        }

        /* 座位列表 */
        .seat-list-card {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            overflow: hidden;
        }

        .seat-table {
            width: 100%;
            border-collapse: collapse;
        }

        .seat-table th,
        .seat-table td {
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid #f0f0f0;
        }

        .seat-table th {
            font-weight: 600;
            color: #555;
            background: #f9f9f9;
        }

        .seat-table tr:hover {
            background: #f9f9f9;
        }

        .seat-status {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
        }

        .status-available {
            background: #e8f5e9;
            color: #4CAF50;
        }

        .status-occupied {
            background: #f5f5f5;
            color: #9e9e9e;
        }

        .status-reserved {
            background: #e3f2fd;
            color: #2196F3;
        }

        .status-temp-leave {
            background: #fff8e1;
            color: #FFC107;
        }

        .status-scattered {
            background: #f8bbd0;
            color: #e91e63;
        }

        .seat-actions {
            display: flex;
        }

        .seat-action {
            margin-right: 10px;
            color: #777;
            cursor: pointer;
        }

        .seat-action:hover {
            color: #b1030d;
        }

        /* 分页 */
        .pagination {
            display: flex;
            justify-content: flex-end;
            padding: 15px 20px;
            border-top: 1px solid #f0f0f0;
        }

        .page-item {
            width: 32px;
            height: 32px;
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 5px;
            cursor: pointer;
        }

        .page-item.active {
            background: #b1030d;
            color: white;
        }

        /* 模态框 */
        .modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1000;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s;
        }

        .modal.show {
            opacity: 1;
            visibility: visible;
        }

        .modal-content {
            width: 400px;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            transform: translateY(20px);
            transition: all 0.3s;
        }

        .modal.show .modal-content {
            transform: translateY(0);
        }

        .modal-header {
            padding: 15px 20px;
            border-bottom: 1px solid #f0f0f0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-title {
            font-size: 18px;
            font-weight: 600;
        }

        .modal-close {
            cursor: pointer;
            color: #777;
        }

        .modal-body {
            padding: 20px;
        }

        .form-group {
            margin-bottom: 15px;
        }

        .form-label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
        }

        .form-input {
            width: 100%;
            height: 40px;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 0 10px;
        }

        .form-select {
            width: 100%;
            height: 40px;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 0 10px;
        }

        .form-textarea {
            width: 100%;
            height: 100px;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 10px;
            resize: vertical;
        }

        .modal-footer {
            padding: 15px 20px;
            border-top: 1px solid #f0f0f0;
            display: flex;
            justify-content: flex-end;
        }

        .modal-button {
            height: 36px;
            padding: 0 15px;
            border-radius: 4px;
            font-weight: 500;
            cursor: pointer;
            margin-left: 10px;
        }

        .button-cancel {
            background: #f5f5f5;
            color: #333;
            border: none;
        }

        .button-confirm {
            background: #b1030d;
            color: white;
            border: none;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .sidebar {
                width: 70px;
            }
            
            .sidebar-title, .sidebar-subtitle, .menu-text {
                display: none;
            }
            
            .menu-icon {
                margin-right: 0;
            }
            
            .main-content {
                margin-left: 70px;
            }
            
            .filter-bar {
                flex-direction: column;
                align-items: flex-start;
            }
            
            .filter-group {
                margin-bottom: 10px;
            }
            
            .seat-grid {
                grid-template-columns: repeat(4, 1fr);
            }
        }
    </style>
</head>
<body>
    <!-- 侧边栏 -->
    <div class="sidebar">
        <div class="sidebar-header">
            <div class="sidebar-title">图书馆座位预约系统</div>
            <div class="sidebar-subtitle">管理后台</div>
        </div>
        <div class="sidebar-menu">
            <a href="admin_dashboard.html" class="menu-item">
                <span class="material-icons menu-icon">dashboard</span>
                <span class="menu-text">仪表盘</span>
            </a>
            <a href="admin_seats.html" class="menu-item active">
                <span class="material-icons menu-icon">event_seat</span>
                <span class="menu-text">座位管理</span>
            </a>
            <a href="admin_rooms.html" class="menu-item">
                <span class="material-icons menu-icon">meeting_room</span>
                <span class="menu-text">教室管理</span>
            </a>
            <a href="admin_bookings.html" class="menu-item">
                <span class="material-icons menu-icon">book_online</span>
                <span class="menu-text">预约记录</span>
            </a>
            <a href="admin_violations.html" class="menu-item">
                <span class="material-icons menu-icon">gavel</span>
                <span class="menu-text">违约记录</span>
            </a>
            <a href="admin_users.html" class="menu-item">
                <span class="material-icons menu-icon">people</span>
                <span class="menu-text">用户管理</span>
            </a>
            <a href="admin_settings.html" class="menu-item">
                <span class="material-icons menu-icon">settings</span>
                <span class="menu-text">系统设置</span>
            </a>
            <a href="admin_login.html" class="menu-item">
                <span class="material-icons menu-icon">logout</span>
                <span class="menu-text">退出登录</span>
            </a>
        </div>
    </div>

    <!-- 主内容区 -->
    <div class="main-content">
        <!-- 顶部导航栏 -->
        <div class="top-nav">
            <div class="page-title">座位管理</div>
            <div class="user-info">
                <span class="user-name">管理员</span>
                <div class="user-avatar">A</div>
            </div>
        </div>

        <!-- 筛选和操作栏 -->
        <div class="filter-bar">
            <div class="filter-group">
                <label class="filter-label">教室:</label>
                <select class="filter-select">
                    <option value="">全部教室</option>
                    <option value="A101">A101 自习室</option>
                    <option value="A102">A102 电子阅览室</option>
                    <option value="B201">B201 安静自习室</option>
                    <option value="B202">B202 研讨室</option>
                    <option value="C301">C301 图书阅览室</option>
                </select>
                
                <label class="filter-label">状态:</label>
                <select class="filter-select">
                    <option value="">全部状态</option>
                    <option value="available">可用</option>
                    <option value="occupied">使用中</option>
                    <option value="reserved">已预约</option>
                    <option value="temp_leave">暂离中</option>
                    <option value="scattered">散座</option>
                </select>
                
                <label class="filter-label">日期:</label>
                <input type="date" class="filter-select" value="2023-05-17">
                
                <label class="filter-label">时间段:</label>
                <select class="filter-select">
                    <option value="">全部时间段</option>
                    <option value="morning">07:00-12:00</option>
                    <option value="afternoon">12:00-17:00</option>
                    <option value="evening">17:00-22:00</option>
                </select>
            </div>
            
            <button class="action-button" onclick="showAddSeatModal()">
                <span class="material-icons">add</span> 添加座位
            </button>
        </div>

        <!-- 座位布局卡片 -->
        <div class="seat-layout-card">
            <div class="card-header">
                <div class="card-title">A101 自习室 - 座位布局</div>
                <div class="card-actions">
                    <span class="material-icons card-action" title="编辑布局">edit</span>
                    <span class="material-icons card-action" title="导出布局">file_download</span>
                    <span class="material-icons card-action" title="打印布局">print</span>
                </div>
            </div>
            <div class="card-content">
                <div class="room-layout">
                    <div class="entrance">
                        <span class="material-icons" style="font-size: 16px; margin-right: 4px;">door_front</span> 入口
                    </div>
                    <div class="seat-grid" id="seatGrid">
                        <!-- 座位将通过JavaScript动态生成 -->
                    </div>
                </div>
            </div>
        </div>

        <!-- 座位列表卡片 -->
        <div class="seat-list-card">
            <div class="card-header">
                <div class="card-title">座位列表</div>
                <div class="card-actions">
                    <span class="material-icons card-action" title="导出列表">file_download</span>
                    <span class="material-icons card-action" title="打印列表">print</span>
                </div>
            </div>
            <table class="seat-table">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>座位号</th>
                        <th>教室</th>
                        <th>状态</th>
                        <th>特点</th>
                        <th>当前使用者</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>1001</td>
                        <td>A1</td>
                        <td>A101 自习室</td>
                        <td><span class="seat-status status-available">可用</span></td>
                        <td>靠窗, 有电源</td>
                        <td>-</td>
                        <td class="seat-actions">
                            <span class="material-icons seat-action" title="编辑">edit</span>
                            <span class="material-icons seat-action" title="禁用">block</span>
                            <span class="material-icons seat-action" title="删除">delete</span>
                        </td>
                    </tr>
                    <tr>
                        <td>1002</td>
                        <td>A2</td>
                        <td>A101 自习室</td>
                        <td><span class="seat-status status-occupied">使用中</span></td>
                        <td>靠窗, 有电源</td>
                        <td>张三 (2020001)</td>
                        <td class="seat-actions">
                            <span class="material-icons seat-action" title="查看">visibility</span>
                            <span class="material-icons seat-action" title="强制签退">logout</span>
                        </td>
                    </tr>
                    <tr>
                        <td>1003</td>
                        <td>A3</td>
                        <td>A101 自习室</td>
                        <td><span class="seat-status status-reserved">已预约</span></td>
                        <td>靠窗, 有电源</td>
                        <td>李四 (2020002)</td>
                        <td class="seat-actions">
                            <span class="material-icons seat-action" title="查看">visibility</span>
                            <span class="material-icons seat-action" title="取消预约">cancel</span>
                        </td>
                    </tr>
                    <tr>
                        <td>1004</td>
                        <td>A4</td>
                        <td>A101 自习室</td>
                        <td><span class="seat-status status-temp-leave">暂离中</span></td>
                        <td>靠窗, 有电源</td>
                        <td>王五 (2020003)</td>
                        <td class="seat-actions">
                            <span class="material-icons seat-action" title="查看">visibility</span>
                            <span class="material-icons seat-action" title="强制签退">logout</span>
                        </td>
                    </tr>
                    <tr>
                        <td>1005</td>
                        <td>A5</td>
                        <td>A101 自习室</td>
                        <td><span class="seat-status status-scattered">散座</span></td>
                        <td>靠窗, 有电源</td>
                        <td>-</td>
                        <td class="seat-actions">
                            <span class="material-icons seat-action" title="编辑">edit</span>
                            <span class="material-icons seat-action" title="禁用">block</span>
                        </td>
                    </tr>
                </tbody>
            </table>
            <div class="pagination">
                <div class="page-item active">1</div>
                <div class="page-item">2</div>
                <div class="page-item">3</div>
                <div class="page-item">4</div>
                <div class="page-item">5</div>
                <div class="page-item">
                    <span class="material-icons">chevron_right</span>
                </div>
            </div>
        </div>
    </div>

    <!-- 添加座位模态框 -->
    <div class="modal" id="addSeatModal">
        <div class="modal-content">
            <div class="modal-header">
                <div class="modal-title">添加座位</div>
                <span class="material-icons modal-close" onclick="closeModal()">close</span>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <label class="form-label">教室</label>
                    <select class="form-select">
                        <option value="A101">A101 自习室</option>
                        <option value="A102">A102 电子阅览室</option>
                        <option value="B201">B201 安静自习室</option>
                        <option value="B202">B202 研讨室</option>
                        <option value="C301">C301 图书阅览室</option>
                    </select>
                </div>
                <div class="form-group">
                    <label class="form-label">座位号</label>
                    <input type="text" class="form-input" placeholder="例如: A6">
                </div>
                <div class="form-group">
                    <label class="form-label">座位特点</label>
                    <div style="display: flex; flex-wrap: wrap; gap: 10px; margin-top: 5px;">
                        <label style="display: flex; align-items: center;">
                            <input type="checkbox" style="margin-right: 5px;"> 靠窗
                        </label>
                        <label style="display: flex; align-items: center;">
                            <input type="checkbox" style="margin-right: 5px;"> 有电源
                        </label>
                        <label style="display: flex; align-items: center;">
                            <input type="checkbox" style="margin-right: 5px;"> 靠走道
                        </label>
                        <label style="display: flex; align-items: center;">
                            <input type="checkbox" style="margin-right: 5px;"> 视野好
                        </label>
                        <label style="display: flex; align-items: center;">
                            <input type="checkbox" style="margin-right: 5px;"> 安静区
                        </label>
                    </div>
                </div>
                <div class="form-group">
                    <label class="form-label">位置坐标</label>
                    <div style="display: flex; gap: 10px;">
                        <input type="number" class="form-input" placeholder="行" style="width: 50%;">
                        <input type="number" class="form-input" placeholder="列" style="width: 50%;">
                    </div>
                </div>
                <div class="form-group">
                    <label class="form-label">备注</label>
                    <textarea class="form-textarea" placeholder="可选备注信息"></textarea>
                </div>
            </div>
            <div class="modal-footer">
                <button class="modal-button button-cancel" onclick="closeModal()">取消</button>
                <button class="modal-button button-confirm" onclick="addSeat()">确认添加</button>
            </div>
        </div>
    </div>

    <script>
        // 座位数据（模拟）
        const seatData = [
            // 0表示无座位，1表示可用，2表示已占用，3表示已预约，4表示暂离中，5表示散座
            [0, 1, 2, 0, 0, 3, 1, 0],
            [1, 2, 3, 4, 1, 1, 2, 1],
            [1, 2, 1, 1, 5, 1, 1, 1],
            [2, 1, 1, 2, 1, 2, 1, 2],
            [1, 1, 2, 1, 1, 1, 2, 1],
            [2, 1, 1, 2, 1, 2, 1, 1]
        ];

        // 初始化座位布局
        function initSeatLayout() {
            const seatGrid = document.getElementById('seatGrid');
            seatGrid.innerHTML = '';

            const rows = ['A', 'B', 'C', 'D', 'E', 'F'];
            
            for (let i = 0; i < seatData.length; i++) {
                for (let j = 0; j < seatData[i].length; j++) {
                    const seatValue = seatData[i][j];
                    const seatId = `${rows[i]}${j+1}`;
                    
                    const seat = document.createElement('div');
                    seat.className = 'seat';
                    
                    if (seatValue === 0) {
                        seat.classList.add('seat-empty');
                    } else if (seatValue === 1) {
                        seat.classList.add('seat-available');
                    } else if (seatValue === 2) {
                        seat.classList.add('seat-occupied');
                    } else if (seatValue === 3) {
                        seat.classList.add('seat-selected');
                    } else if (seatValue === 4) {
                        seat.classList.add('seat-occupied');
                        seat.style.opacity = '0.7';
                    } else if (seatValue === 5) {
                        seat.style.background = '#e91e63';
                    }
                    
                    seat.textContent = seatId;
                    seat.onclick = function() {
                        alert(`选择了座位: ${seatId}`);
                    };
                    
                    seatGrid.appendChild(seat);
                }
            }
        }

        // 显示添加座位模态框
        function showAddSeatModal() {
            document.getElementById('addSeatModal').classList.add('show');
        }

        // 关闭模态框
        function closeModal() {
            document.getElementById('addSeatModal').classList.remove('show');
        }

        // 添加座位
        function addSeat() {
            alert('座位添加成功！');
            closeModal();
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            initSeatLayout();
        });
    </script>
</body>
</html>
