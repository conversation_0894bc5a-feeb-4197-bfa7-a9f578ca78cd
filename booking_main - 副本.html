<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>预约主页 - 图书馆座位预约</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            -webkit-tap-highlight-color: transparent;
            font-family: "PingFang SC", "Helvetica Neue", Arial, sans-serif;
        }

        body {
            background-color: #f8f8f8;
            color: #333;
            font-size: 14px;
            line-height: 1.5;
            overflow-x: hidden;
        }

        /* 模拟iPhone 15 Pro Max */
        .device-container {
            width: 100%;
            max-width: 430px;
            height: 932px;
            margin: 20px auto;
            position: relative;
            overflow: hidden;
            border-radius: 55px;
            box-shadow: 0 20px 50px rgba(0, 0, 0, 0.3);
            background: #1a1a1a;
            padding: 12px;
        }

        /* 刘海区域 */
        .notch {
            position: absolute;
            width: 160px;
            height: 34px;
            background: #1a1a1a;
            top: 12px;
            left: 50%;
            transform: translateX(-50%);
            border-radius: 0 0 20px 20px;
            z-index: 100;
        }

        /* 底部指示条 */
        .home-indicator {
            position: absolute;
            width: 140px;
            height: 5px;
            background: #ffffff;
            bottom: 25px;
            left: 50%;
            transform: translateX(-50%);
            border-radius: 3px;
            z-index: 100;
        }

        /* 手机屏幕 */
        .screen {
            width: 100%;
            height: 100%;
            background: #ffffff;
            border-radius: 45px;
            overflow: hidden;
            position: relative;
        }

        /* 小程序内容区域 */
        .app-container {
            width: 100%;
            height: 100%;
            overflow: hidden;
            display: flex;
            flex-direction: column;
        }

        /* 顶部导航栏 */
        .nav-bar {
            height: 88px;
            background-color: #b1030d;
            color: white;
            display: flex;
            justify-content: space-between;
            align-items: flex-end;
            padding: 0 16px 10px;
            position: relative;
            z-index: 10;
        }

        .nav-title {
            font-size: 18px;
            font-weight: 600;
            padding-bottom: 10px;
        }

        .nav-right {
            font-size: 24px;
            padding-bottom: 10px;
        }

        /* 主内容区 */
        .main-content {
            flex: 1;
            overflow-y: auto;
            -webkit-overflow-scrolling: touch;
            padding: 16px;
            padding-bottom: 70px;
        }

        /* 日期选择器 */
        .date-selector {
            margin-bottom: 20px;
        }

        .date-title {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 12px;
            color: #333;
        }

        .date-scroll {
            display: flex;
            overflow-x: auto;
            -webkit-overflow-scrolling: touch;
            padding: 8px 0;
            margin: 0 -16px;
            padding-left: 16px;
        }

        .date-scroll::-webkit-scrollbar {
            display: none;
        }

        .date-item {
            min-width: 60px;
            height: 80px;
            margin-right: 12px;
            border-radius: 12px;
            background: white;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
            border: 1px solid #eee;
            transition: all 0.3s ease;
        }

        .date-item.active {
            background: #b1030d;
            transform: scale(1.05);
            box-shadow: 0 4px 12px rgba(177, 3, 13, 0.2);
        }

        .date-day {
            font-size: 22px;
            font-weight: bold;
            margin-bottom: 4px;
        }

        .date-weekday {
            font-size: 12px;
            color: #666;
        }

        .date-item.active .date-day,
        .date-item.active .date-weekday {
            color: white;
        }

        /* 时间段选择 */
        .time-selector {
            margin-bottom: 20px;
        }

        .time-title {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 12px;
            color: #333;
        }

        .time-options {
            display: flex;
            justify-content: space-between;
        }

        .time-item {
            flex: 1;
            height: 60px;
            margin: 0 6px;
            border-radius: 12px;
            background: white;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
            border: 1px solid #eee;
            transition: all 0.3s ease;
        }

        .time-item:first-child {
            margin-left: 0;
        }

        .time-item:last-child {
            margin-right: 0;
        }

        .time-item.active {
            background: #b1030d;
            color: white;
            transform: scale(1.05);
            box-shadow: 0 4px 12px rgba(177, 3, 13, 0.2);
        }

        .time-range {
            font-size: 14px;
            font-weight: 600;
        }

        .time-label {
            font-size: 12px;
            color: #666;
            margin-top: 4px;
        }

        .time-item.active .time-label {
            color: rgba(255, 255, 255, 0.8);
        }

        /* 教室列表 */
        .room-list {
            margin-bottom: 20px;
        }

        .room-title {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 12px;
            color: #333;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .room-count {
            font-size: 14px;
            color: #666;
            font-weight: normal;
        }

        .room-item {
            background: white;
            border-radius: 16px;
            padding: 16px;
            margin-bottom: 12px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            position: relative;
            border-left: 4px solid #eacdcf;
            transition: all 0.3s ease;
        }

        .room-item:active {
            transform: scale(0.98);
            box-shadow: 0 1px 5px rgba(0, 0, 0, 0.1);
        }

        .room-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }

        .room-name {
            font-size: 16px;
            font-weight: 600;
            color: #333;
        }

        .room-available {
            font-size: 14px;
            color: #b1030d;
            font-weight: 600;
        }

        .room-info {
            display: flex;
            font-size: 12px;
            color: #666;
        }

        .room-info-item {
            margin-right: 12px;
            display: flex;
            align-items: center;
        }

        .room-info-item i {
            margin-right: 4px;
            font-size: 14px;
        }

        /* 底部导航 */
        .tab-bar {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: white;
            display: flex;
            box-shadow: 0 -1px 10px rgba(0, 0, 0, 0.05);
            z-index: 100;
        }

        .tab-item {
            flex: 1;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            color: #999;
            font-size: 10px;
            transition: all 0.3s ease;
        }

        .tab-item.active {
            color: #b1030d;
        }

        .tab-icon {
            font-size: 24px;
            margin-bottom: 2px;
        }

        /* 加载动画 */
        @keyframes pulse {
            0% {
                transform: scale(0.95);
                opacity: 0.7;
            }
            50% {
                transform: scale(1);
                opacity: 1;
            }
            100% {
                transform: scale(0.95);
                opacity: 0.7;
            }
        }

        .loading-indicator {
            width: 100%;
            height: 100px;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .loading-dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background-color: #b1030d;
            margin: 0 4px;
            animation: pulse 1.5s infinite;
        }

        .loading-dot:nth-child(2) {
            animation-delay: 0.2s;
        }

        .loading-dot:nth-child(3) {
            animation-delay: 0.4s;
        }

        /* 毛玻璃效果 */
        .glass-effect {
            background: rgba(255, 255, 255, 0.7);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
        }

        /* 动画效果 */
        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .animate-in {
            animation: fadeIn 0.5s ease forwards;
        }

        .room-item {
            animation: fadeIn 0.5s ease forwards;
            animation-delay: calc(var(--i) * 0.1s);
            opacity: 0;
        }

        /* 图标字体 */
        .icon {
            font-family: "Material Icons";
            font-weight: normal;
            font-style: normal;
            font-size: 24px;
            display: inline-block;
            line-height: 1;
            text-transform: none;
            letter-spacing: normal;
            word-wrap: normal;
            white-space: nowrap;
            direction: ltr;
            -webkit-font-smoothing: antialiased;
            text-rendering: optimizeLegibility;
            -moz-osx-font-smoothing: grayscale;
            font-feature-settings: 'liga';
        }
    </style>
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
</head>
<body>
    <div class="device-container">
        <div class="notch"></div>
        <div class="home-indicator"></div>
        <div class="screen">
            <div class="app-container">
                <!-- 顶部导航栏 -->
                <div class="nav-bar">
                    <div></div>
                    <div class="nav-title">预约座位</div>
                    <div class="nav-right">
                        <i class="icon">person</i>
                    </div>
                </div>

                <!-- 主内容区 -->
                <div class="main-content">
                    <!-- 日期选择器 -->
                    <div class="date-selector animate-in">
                        <div class="date-title">选择日期</div>
                        <div class="date-scroll">
                            <div class="date-item" onclick="selectDate(this)">
                                <div class="date-day">今天</div>
                                <div class="date-weekday">5/15</div>
                            </div>
                            <div class="date-item" onclick="selectDate(this)">
                                <div class="date-day">16</div>
                                <div class="date-weekday">周四</div>
                            </div>
                            <div class="date-item active" onclick="selectDate(this)">
                                <div class="date-day">17</div>
                                <div class="date-weekday">周五</div>
                            </div>
                            <div class="date-item" onclick="selectDate(this)">
                                <div class="date-day">18</div>
                                <div class="date-weekday">周六</div>
                            </div>
                            <div class="date-item" onclick="selectDate(this)">
                                <div class="date-day">19</div>
                                <div class="date-weekday">周日</div>
                            </div>
                            <div class="date-item" onclick="selectDate(this)">
                                <div class="date-day">20</div>
                                <div class="date-weekday">周一</div>
                            </div>
                            <div class="date-item" onclick="selectDate(this)">
                                <div class="date-day">21</div>
                                <div class="date-weekday">周二</div>
                            </div>
                        </div>
                    </div>

                    <!-- 时间段选择 -->
                    <div class="time-selector animate-in" style="animation-delay: 0.1s;">
                        <div class="time-title">选择时间段</div>
                        <div class="time-options">
                            <div class="time-item" onclick="selectTime(this)">
                                <div class="time-range">07:00-12:00</div>
                                <div class="time-label">上午</div>
                            </div>
                            <div class="time-item active" onclick="selectTime(this)">
                                <div class="time-range">12:00-17:00</div>
                                <div class="time-label">下午</div>
                            </div>
                            <div class="time-item" onclick="selectTime(this)">
                                <div class="time-range">17:00-22:00</div>
                                <div class="time-label">晚上</div>
                            </div>
                        </div>
                    </div>

                    <!-- 教室列表 -->
                    <div class="room-list animate-in" style="animation-delay: 0.2s;">
                        <div class="room-title">
                            可用教室 <span class="room-count">共8个</span>
                        </div>
                        
                        <div class="room-item" style="--i:1;" onclick="selectRoom('A101')">
                            <div class="room-header">
                                <div class="room-name">A101 自习室</div>
                                <div class="room-available">可用: 45/80</div>
                            </div>
                            <div class="room-info">
                                <div class="room-info-item">
                                    <i class="icon" style="font-size: 14px;">wifi</i> 有WiFi
                                </div>
                                <div class="room-info-item">
                                    <i class="icon" style="font-size: 14px;">power</i> 有电源
                                </div>
                                <div class="room-info-item">
                                    <i class="icon" style="font-size: 14px;">ac_unit</i> 有空调
                                </div>
                            </div>
                        </div>

                        <div class="room-item" style="--i:2;" onclick="selectRoom('A102')">
                            <div class="room-header">
                                <div class="room-name">A102 电子阅览室</div>
                                <div class="room-available">可用: 28/50</div>
                            </div>
                            <div class="room-info">
                                <div class="room-info-item">
                                    <i class="icon" style="font-size: 14px;">computer</i> 有电脑
                                </div>
                                <div class="room-info-item">
                                    <i class="icon" style="font-size: 14px;">power</i> 有电源
                                </div>
                                <div class="room-info-item">
                                    <i class="icon" style="font-size: 14px;">ac_unit</i> 有空调
                                </div>
                            </div>
                        </div>

                        <div class="room-item" style="--i:3;" onclick="selectRoom('B201')">
                            <div class="room-header">
                                <div class="room-name">B201 安静自习室</div>
                                <div class="room-available">可用: 60/100</div>
                            </div>
                            <div class="room-info">
                                <div class="room-info-item">
                                    <i class="icon" style="font-size: 14px;">volume_off</i> 安静区域
                                </div>
                                <div class="room-info-item">
                                    <i class="icon" style="font-size: 14px;">power</i> 有电源
                                </div>
                                <div class="room-info-item">
                                    <i class="icon" style="font-size: 14px;">ac_unit</i> 有空调
                                </div>
                            </div>
                        </div>

                        <div class="room-item" style="--i:4;" onclick="selectRoom('B202')">
                            <div class="room-header">
                                <div class="room-name">B202 研讨室</div>
                                <div class="room-available">可用: 15/30</div>
                            </div>
                            <div class="room-info">
                                <div class="room-info-item">
                                    <i class="icon" style="font-size: 14px;">group</i> 小组讨论
                                </div>
                                <div class="room-info-item">
                                    <i class="icon" style="font-size: 14px;">power</i> 有电源
                                </div>
                                <div class="room-info-item">
                                    <i class="icon" style="font-size: 14px;">wifi</i> 有WiFi
                                </div>
                            </div>
                        </div>

                        <div class="room-item" style="--i:5;" onclick="selectRoom('C301')">
                            <div class="room-header">
                                <div class="room-name">C301 图书阅览室</div>
                                <div class="room-available">可用: 38/70</div>
                            </div>
                            <div class="room-info">
                                <div class="room-info-item">
                                    <i class="icon" style="font-size: 14px;">menu_book</i> 藏书区
                                </div>
                                <div class="room-info-item">
                                    <i class="icon" style="font-size: 14px;">weekend</i> 沙发座
                                </div>
                                <div class="room-info-item">
                                    <i class="icon" style="font-size: 14px;">ac_unit</i> 有空调
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 底部导航栏 -->
                <div class="tab-bar">
                    <div class="tab-item active">
                        <i class="icon tab-icon">event_seat</i>
                        <div>预约</div>
                    </div>
                    <div class="tab-item">
                        <i class="icon tab-icon">schedule</i>
                        <div>我的座位</div>
                    </div>
                    <div class="tab-item">
                        <i class="icon tab-icon">person</i>
                        <div>我的</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 日期选择
        function selectDate(element) {
            // 移除所有日期项的active类
            document.querySelectorAll('.date-item').forEach(item => {
                item.classList.remove('active');
            });
            // 为当前选中项添加active类
            element.classList.add('active');
            
            // 模拟加载新数据
            simulateLoading();
        }

        // 时间段选择
        function selectTime(element) {
            // 移除所有时间项的active类
            document.querySelectorAll('.time-item').forEach(item => {
                item.classList.remove('active');
            });
            // 为当前选中项添加active类
            element.classList.add('active');
            
            // 模拟加载新数据
            simulateLoading();
        }

        // 选择教室
        function selectRoom(roomId) {
            // 在实际应用中，这里会跳转到座位选择页面
            alert('即将进入 ' + roomId + ' 座位选择页面');
            // 实际应用中应该使用页面跳转
            // window.location.href = 'seat_selection.html?room=' + roomId;
        }

        // 模拟加载效果
        function simulateLoading() {
            const roomList = document.querySelector('.room-list');
            const originalContent = roomList.innerHTML;
            
            // 显示加载动画
            roomList.innerHTML = `
                <div class="room-title">
                    加载中...
                </div>
                <div class="loading-indicator">
                    <div class="loading-dot"></div>
                    <div class="loading-dot"></div>
                    <div class="loading-dot"></div>
                </div>
            `;
            
            // 模拟网络请求延迟
            setTimeout(() => {
                // 恢复原始内容
                roomList.innerHTML = originalContent;
                
                // 重新应用动画
                document.querySelectorAll('.room-item').forEach(item => {
                    item.style.opacity = '0';
                    void item.offsetWidth; // 触发重绘
                });
            }, 800);
        }

        // 页面加载完成后，为每个教室项设置动画延迟
        document.addEventListener('DOMContentLoaded', function() {
            document.querySelectorAll('.room-item').forEach((item, index) => {
                item.style.setProperty('--i', index + 1);
            });
        });
    </script>
</body>
</html>
