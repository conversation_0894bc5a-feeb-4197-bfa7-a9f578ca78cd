<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>座位选择 - 图书馆座位预约</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            -webkit-tap-highlight-color: transparent;
            font-family: "PingFang SC", "Helvetica Neue", Arial, sans-serif;
        }

        body {
            background-color: #f8f8f8;
            color: #333;
            font-size: 14px;
            line-height: 1.5;
            overflow-x: hidden;
        }

        /* 模拟iPhone 15 Pro Max */
        .device-container {
            width: 100%;
            max-width: 430px;
            height: 932px;
            margin: 20px auto;
            position: relative;
            overflow: hidden;
            border-radius: 55px;
            box-shadow: 0 20px 50px rgba(0, 0, 0, 0.3);
            background: #1a1a1a;
            padding: 12px;
        }

        /* 刘海区域 */
        .notch {
            position: absolute;
            width: 160px;
            height: 34px;
            background: #1a1a1a;
            top: 12px;
            left: 50%;
            transform: translateX(-50%);
            border-radius: 0 0 20px 20px;
            z-index: 100;
        }

        /* 底部指示条 */
        .home-indicator {
            position: absolute;
            width: 140px;
            height: 5px;
            background: #ffffff;
            bottom: 25px;
            left: 50%;
            transform: translateX(-50%);
            border-radius: 3px;
            z-index: 100;
        }

        /* 手机屏幕 */
        .screen {
            width: 100%;
            height: 100%;
            background: #ffffff;
            border-radius: 45px;
            overflow: hidden;
            position: relative;
        }

        /* 小程序内容区域 */
        .app-container {
            width: 100%;
            height: 100%;
            overflow: hidden;
            display: flex;
            flex-direction: column;
        }

        /* 顶部导航栏 */
        .nav-bar {
            height: 88px;
            background-color: #b1030d;
            color: white;
            display: flex;
            justify-content: space-between;
            align-items: flex-end;
            padding: 0 16px 10px;
            position: relative;
            z-index: 10;
        }

        .nav-left {
            font-size: 24px;
            padding-bottom: 10px;
        }

        .nav-title {
            font-size: 18px;
            font-weight: 600;
            padding-bottom: 10px;
        }

        .nav-right {
            width: 24px;
            padding-bottom: 10px;
        }

        /* 主内容区 */
        .main-content {
            flex: 1;
            overflow-y: auto;
            -webkit-overflow-scrolling: touch;
            padding: 16px;
            padding-bottom: 90px;
        }

        /* 教室信息 */
        .room-info {
            background: white;
            border-radius: 16px;
            padding: 16px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
        }

        .room-info-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 12px;
        }

        .room-name {
            font-size: 18px;
            font-weight: 600;
            color: #333;
        }

        .room-available {
            font-size: 14px;
            color: #b1030d;
            font-weight: 600;
        }

        .room-details {
            display: flex;
            font-size: 13px;
            color: #666;
        }

        .room-detail-item {
            margin-right: 12px;
            display: flex;
            align-items: center;
        }

        .room-detail-item i {
            margin-right: 4px;
            font-size: 16px;
        }

        /* 座位筛选 */
        .seat-filter {
            display: flex;
            overflow-x: auto;
            -webkit-overflow-scrolling: touch;
            margin: 0 -16px 16px;
            padding: 0 16px;
        }

        .seat-filter::-webkit-scrollbar {
            display: none;
        }

        .filter-item {
            display: flex;
            align-items: center;
            height: 32px;
            padding: 0 12px;
            margin-right: 8px;
            border-radius: 16px;
            background: white;
            font-size: 13px;
            color: #666;
            white-space: nowrap;
            border: 1px solid #eee;
        }

        .filter-item.active {
            background: #b1030d;
            color: white;
            border-color: #b1030d;
        }

        .filter-item i {
            margin-right: 4px;
            font-size: 16px;
        }

        /* 座位图例 */
        .seat-legend {
            display: flex;
            justify-content: center;
            margin-bottom: 16px;
        }

        .legend-item {
            display: flex;
            align-items: center;
            margin: 0 8px;
            font-size: 12px;
            color: #666;
        }

        .legend-color {
            width: 16px;
            height: 16px;
            border-radius: 4px;
            margin-right: 4px;
        }

        .legend-available {
            background-color: #4CAF50;
        }

        .legend-occupied {
            background-color: #9e9e9e;
        }

        .legend-selected {
            background-color: #b1030d;
        }

        /* 座位布局 */
        .seat-layout-container {
            position: relative;
            overflow: hidden;
            margin-bottom: 20px;
            border-radius: 16px;
            background: white;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
        }

        .seat-layout-header {
            padding: 12px 16px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 1px solid #f0f0f0;
        }

        .layout-title {
            font-size: 16px;
            font-weight: 600;
            color: #333;
        }

        .zoom-controls {
            display: flex;
        }

        .zoom-btn {
            width: 32px;
            height: 32px;
            border-radius: 16px;
            background: #f5f5f5;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-left: 8px;
            color: #666;
        }

        .seat-layout {
            padding: 20px;
            overflow: auto;
            -webkit-overflow-scrolling: touch;
            min-height: 300px;
            display: flex;
            flex-direction: column;
            align-items: center;
        }

        .entrance {
            width: 120px;
            height: 30px;
            background: #eacdcf;
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            color: #b1030d;
            margin-bottom: 20px;
        }

        .seat-grid {
            display: grid;
            grid-template-columns: repeat(8, 1fr);
            gap: 10px;
            width: 100%;
            max-width: 400px;
        }

        .seat {
            width: 36px;
            height: 36px;
            border-radius: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: 600;
            color: white;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .seat-available {
            background-color: #4CAF50;
        }

        .seat-occupied {
            background-color: #9e9e9e;
        }

        .seat-selected {
            background-color: #b1030d;
            transform: scale(1.05);
            box-shadow: 0 2px 8px rgba(177, 3, 13, 0.3);
        }

        .seat-empty {
            visibility: hidden;
        }

        /* 座位信息弹窗 */
        .seat-info-popup {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: white;
            border-radius: 20px 20px 0 0;
            padding: 20px;
            box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.1);
            transform: translateY(100%);
            transition: transform 0.3s ease;
            z-index: 1000;
        }

        .seat-info-popup.show {
            transform: translateY(0);
        }

        .popup-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }

        .popup-title {
            font-size: 18px;
            font-weight: 600;
            color: #333;
        }

        .close-btn {
            width: 30px;
            height: 30px;
            border-radius: 15px;
            background: #f5f5f5;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #666;
        }

        .seat-features {
            display: flex;
            flex-wrap: wrap;
            margin-bottom: 16px;
        }

        .seat-feature {
            display: flex;
            align-items: center;
            margin-right: 16px;
            margin-bottom: 8px;
            font-size: 14px;
            color: #666;
        }

        .seat-feature i {
            margin-right: 4px;
            color: #b1030d;
        }

        /* 底部确认按钮 */
        .bottom-bar {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: white;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 16px;
            box-shadow: 0 -1px 10px rgba(0, 0, 0, 0.05);
            z-index: 100;
        }

        .confirm-btn {
            width: 100%;
            height: 50px;
            border-radius: 25px;
            background: #b1030d;
            color: white;
            font-size: 16px;
            font-weight: 600;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
        }

        .confirm-btn.disabled {
            background: #cccccc;
            color: #ffffff;
        }

        /* 图标字体 */
        .icon {
            font-family: "Material Icons";
            font-weight: normal;
            font-style: normal;
            font-size: 24px;
            display: inline-block;
            line-height: 1;
            text-transform: none;
            letter-spacing: normal;
            word-wrap: normal;
            white-space: nowrap;
            direction: ltr;
            -webkit-font-smoothing: antialiased;
            text-rendering: optimizeLegibility;
            -moz-osx-font-smoothing: grayscale;
            font-feature-settings: 'liga';
        }

        /* 动画效果 */
        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .animate-in {
            animation: fadeIn 0.5s ease forwards;
        }

        .seat {
            animation: fadeIn 0.3s ease forwards;
            animation-delay: calc(var(--i) * 0.02s);
            opacity: 0;
        }
    </style>
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
</head>
<body>
    <div class="device-container">
        <div class="notch"></div>
        <div class="home-indicator"></div>
        <div class="screen">
            <div class="app-container">
                <!-- 顶部导航栏 -->
                <div class="nav-bar">
                    <div class="nav-left">
                        <i class="icon" onclick="goBack()">arrow_back</i>
                    </div>
                    <div class="nav-title">选择座位</div>
                    <div class="nav-right"></div>
                </div>

                <!-- 主内容区 -->
                <div class="main-content">
                    <!-- 教室信息 -->
                    <div class="room-info animate-in">
                        <div class="room-info-header">
                            <div class="room-name">A101 自习室</div>
                            <div class="room-available">可用: 45/80</div>
                        </div>
                        <div class="room-details">
                            <div class="room-detail-item">
                                <i class="icon" style="font-size: 16px;">location_on</i> 图书馆1楼
                            </div>
                            <div class="room-detail-item">
                                <i class="icon" style="font-size: 16px;">access_time</i> 07:00-22:00
                            </div>
                            <div class="room-detail-item">
                                <i class="icon" style="font-size: 16px;">wifi</i> 有WiFi
                            </div>
                        </div>
                    </div>

                    <!-- 座位筛选 -->
                    <div class="seat-filter animate-in" style="animation-delay: 0.1s;">
                        <div class="filter-item active">
                            <i class="icon" style="font-size: 16px;">view_module</i> 全部
                        </div>
                        <div class="filter-item">
                            <i class="icon" style="font-size: 16px;">power</i> 有电源
                        </div>
                        <div class="filter-item">
                            <i class="icon" style="font-size: 16px;">window</i> 靠窗
                        </div>
                        <div class="filter-item">
                            <i class="icon" style="font-size: 16px;">desktop_windows</i> 靠走道
                        </div>
                        <div class="filter-item">
                            <i class="icon" style="font-size: 16px;">visibility</i> 视野好
                        </div>
                    </div>

                    <!-- 座位图例 -->
                    <div class="seat-legend animate-in" style="animation-delay: 0.15s;">
                        <div class="legend-item">
                            <div class="legend-color legend-available"></div>
                            <span>可选</span>
                        </div>
                        <div class="legend-item">
                            <div class="legend-color legend-occupied"></div>
                            <span>已占</span>
                        </div>
                        <div class="legend-item">
                            <div class="legend-color legend-selected"></div>
                            <span>已选</span>
                        </div>
                    </div>

                    <!-- 座位布局 -->
                    <div class="seat-layout-container animate-in" style="animation-delay: 0.2s;">
                        <div class="seat-layout-header">
                            <div class="layout-title">座位布局图</div>
                            <div class="zoom-controls">
                                <div class="zoom-btn" onclick="zoomOut()">
                                    <i class="icon" style="font-size: 20px;">remove</i>
                                </div>
                                <div class="zoom-btn" onclick="zoomIn()">
                                    <i class="icon" style="font-size: 20px;">add</i>
                                </div>
                            </div>
                        </div>
                        <div class="seat-layout">
                            <div class="entrance">
                                <i class="icon" style="font-size: 16px;">door_front</i> 入口
                            </div>
                            <div class="seat-grid" id="seatGrid">
                                <!-- 座位将通过JavaScript动态生成 -->
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 底部确认按钮 -->
                <div class="bottom-bar">
                    <div class="confirm-btn disabled" id="confirmBtn">
                        确认选座
                    </div>
                </div>

                <!-- 座位信息弹窗 -->
                <div class="seat-info-popup" id="seatInfoPopup">
                    <div class="popup-header">
                        <div class="popup-title">座位详情</div>
                        <div class="close-btn" onclick="closeSeatInfo()">
                            <i class="icon" style="font-size: 20px;">close</i>
                        </div>
                    </div>
                    <div class="seat-features" id="seatFeatures">
                        <!-- 座位特点将通过JavaScript动态生成 -->
                    </div>
                    <div class="confirm-btn" onclick="selectCurrentSeat()">
                        选择此座位
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 座位数据（模拟）
        const seatData = [
            // 0表示无座位，1表示可用，2表示已占用
            [0, 1, 1, 0, 0, 1, 1, 0],
            [1, 1, 2, 2, 1, 1, 2, 1],
            [1, 2, 1, 1, 2, 1, 1, 1],
            [2, 1, 1, 2, 1, 2, 1, 2],
            [1, 1, 2, 1, 1, 1, 2, 1],
            [2, 1, 1, 2, 1, 2, 1, 1],
            [1, 2, 1, 1, 2, 1, 1, 2],
            [1, 1, 2, 1, 1, 2, 1, 1],
            [2, 1, 1, 2, 1, 1, 2, 1],
            [1, 2, 1, 1, 2, 1, 1, 2]
        ];

        // 座位特点数据（模拟）
        const seatFeatures = {
            "A1": ["靠窗", "有电源", "安静区"],
            "A2": ["靠窗", "视野好"],
            "A3": ["靠窗", "有电源"],
            "B1": ["有电源", "靠走道"],
            "B2": ["视野好", "安静区"],
            "B3": ["有电源", "靠走道"],
            "C1": ["靠窗", "有电源", "视野好"],
            "C2": ["靠窗", "安静区"],
            "C3": ["有电源", "视野好"],
            // ... 其他座位特点
        };

        // 座位图标数据
        const featureIcons = {
            "靠窗": "window",
            "有电源": "power",
            "靠走道": "desktop_windows",
            "视野好": "visibility",
            "安静区": "volume_off"
        };

        // 当前选中的座位
        let selectedSeat = null;
        let currentSeat = null;
        let scale = 1;

        // 初始化座位布局
        function initSeatLayout() {
            const seatGrid = document.getElementById('seatGrid');
            seatGrid.innerHTML = '';

            const rows = ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J'];
            
            for (let i = 0; i < seatData.length; i++) {
                for (let j = 0; j < seatData[i].length; j++) {
                    const seatValue = seatData[i][j];
                    const seatId = `${rows[i]}${j+1}`;
                    
                    const seat = document.createElement('div');
                    seat.className = 'seat';
                    seat.style.setProperty('--i', i * seatData[i].length + j);
                    
                    if (seatValue === 0) {
                        seat.classList.add('seat-empty');
                    } else if (seatValue === 1) {
                        seat.classList.add('seat-available');
                        seat.onclick = function() {
                            showSeatInfo(seatId);
                        };
                    } else if (seatValue === 2) {
                        seat.classList.add('seat-occupied');
                    }
                    
                    seat.textContent = seatId;
                    seatGrid.appendChild(seat);
                }
            }
        }

        // 显示座位信息
        function showSeatInfo(seatId) {
            currentSeat = seatId;
            
            const featuresContainer = document.getElementById('seatFeatures');
            featuresContainer.innerHTML = '';
            
            const features = seatFeatures[seatId] || [];
            
            // 添加座位号
            const seatNumberFeature = document.createElement('div');
            seatNumberFeature.className = 'seat-feature';
            seatNumberFeature.innerHTML = `<i class="icon" style="font-size: 18px;">event_seat</i> 座位号: ${seatId}`;
            featuresContainer.appendChild(seatNumberFeature);
            
            // 添加其他特点
            features.forEach(feature => {
                const featureElement = document.createElement('div');
                featureElement.className = 'seat-feature';
                
                const icon = featureIcons[feature] || 'check';
                featureElement.innerHTML = `<i class="icon" style="font-size: 18px;">${icon}</i> ${feature}`;
                
                featuresContainer.appendChild(featureElement);
            });
            
            // 显示弹窗
            document.getElementById('seatInfoPopup').classList.add('show');
        }

        // 关闭座位信息
        function closeSeatInfo() {
            document.getElementById('seatInfoPopup').classList.remove('show');
            currentSeat = null;
        }

        // 选择当前座位
        function selectCurrentSeat() {
            if (currentSeat) {
                // 如果之前有选中的座位，恢复其样式
                if (selectedSeat) {
                    const prevSelected = document.querySelector(`.seat-selected`);
                    if (prevSelected) {
                        prevSelected.classList.remove('seat-selected');
                        prevSelected.classList.add('seat-available');
                    }
                }
                
                // 更新当前选中的座位
                selectedSeat = currentSeat;
                
                // 更新座位样式
                const seats = document.querySelectorAll('.seat');
                seats.forEach(seat => {
                    if (seat.textContent === currentSeat) {
                        seat.classList.remove('seat-available');
                        seat.classList.add('seat-selected');
                    }
                });
                
                // 启用确认按钮
                document.getElementById('confirmBtn').classList.remove('disabled');
                
                // 关闭弹窗
                closeSeatInfo();
            }
        }

        // 缩放功能
        function zoomIn() {
            if (scale < 1.5) {
                scale += 0.1;
                updateScale();
            }
        }

        function zoomOut() {
            if (scale > 0.7) {
                scale -= 0.1;
                updateScale();
            }
        }

        function updateScale() {
            const seatGrid = document.getElementById('seatGrid');
            seatGrid.style.transform = `scale(${scale})`;
            seatGrid.style.transformOrigin = 'center top';
        }

        // 返回上一页
        function goBack() {
            // 实际应用中应该使用微信小程序的导航API
            alert('返回上一页');
            // wx.navigateBack();
        }

        // 确认选座
        document.getElementById('confirmBtn').addEventListener('click', function() {
            if (!this.classList.contains('disabled') && selectedSeat) {
                // 实际应用中应该跳转到确认页面
                alert(`已选择座位: ${selectedSeat}，即将跳转到确认页面`);
                // wx.navigateTo({url: 'booking_confirmation.html?seat=' + selectedSeat});
            }
        });

        // 筛选功能
        document.querySelectorAll('.filter-item').forEach(item => {
            item.addEventListener('click', function() {
                document.querySelectorAll('.filter-item').forEach(el => {
                    el.classList.remove('active');
                });
                this.classList.add('active');
                
                // 实际应用中应该根据筛选条件更新座位显示
                // 这里只做简单的模拟
                const filterText = this.textContent.trim();
                alert(`已选择筛选条件: ${filterText}`);
            });
        });

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            initSeatLayout();
        });
    </script>
</body>
</html>
