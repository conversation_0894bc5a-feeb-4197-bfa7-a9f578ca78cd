<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>图书馆座位预约系统 - 预览</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: "PingFang SC", "Helvetica Neue", Arial, sans-serif;
        }

        body {
            background-color: #f5f5f5;
            color: #333;
            font-size: 14px;
            line-height: 1.5;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
        }

        .title {
            font-size: 28px;
            font-weight: 600;
            color: #b1030d;
            margin-bottom: 10px;
        }

        .subtitle {
            font-size: 16px;
            color: #666;
        }

        .section {
            margin-bottom: 40px;
        }

        .section-title {
            font-size: 22px;
            font-weight: 600;
            color: #333;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 1px solid #eee;
        }

        .pages-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
            gap: 20px;
        }

        .page-card {
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
        }

        .page-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.15);
        }

        .page-thumbnail {
            width: 100%;
            height: 200px;
            background: #f8f8f8;
            position: relative;
            overflow: hidden;
        }

        .page-thumbnail iframe {
            width: 100%;
            height: 400px;
            border: none;
            transform: scale(0.5);
            transform-origin: 0 0;
            pointer-events: none;
        }

        .page-info {
            padding: 15px;
        }

        .page-name {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 5px;
        }

        .page-desc {
            font-size: 13px;
            color: #666;
            margin-bottom: 10px;
            height: 40px;
            overflow: hidden;
        }

        .page-actions {
            display: flex;
            justify-content: space-between;
        }

        .page-button {
            padding: 8px 12px;
            border-radius: 5px;
            font-size: 13px;
            font-weight: 500;
            cursor: pointer;
            text-decoration: none;
            text-align: center;
        }

        .view-button {
            background: #b1030d;
            color: white;
            flex: 1;
            margin-right: 10px;
        }

        .code-button {
            background: #f5f5f5;
            color: #333;
            width: 40px;
        }

        .flow-section {
            margin-bottom: 40px;
        }

        .flow-title {
            font-size: 20px;
            font-weight: 600;
            margin-bottom: 20px;
        }

        .flow-diagram {
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            overflow-x: auto;
        }

        .flow-steps {
            display: flex;
            align-items: center;
            min-width: 900px;
        }

        .flow-step {
            display: flex;
            flex-direction: column;
            align-items: center;
            text-align: center;
            width: 140px;
        }

        .step-icon {
            width: 60px;
            height: 60px;
            border-radius: 30px;
            background: #f5f5f5;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 10px;
            font-size: 24px;
            color: #b1030d;
        }

        .step-name {
            font-size: 14px;
            font-weight: 500;
            margin-bottom: 5px;
        }

        .step-desc {
            font-size: 12px;
            color: #666;
        }

        .flow-arrow {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #999;
            font-size: 24px;
        }

        .footer {
            text-align: center;
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #eee;
            color: #999;
            font-size: 13px;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .pages-grid {
                grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
            }
        }

        @media (max-width: 480px) {
            .pages-grid {
                grid-template-columns: 1fr;
            }
        }

        /* 图标字体 */
        .icon {
            font-family: "Material Icons";
            font-weight: normal;
            font-style: normal;
            font-size: 24px;
            display: inline-block;
            line-height: 1;
            text-transform: none;
            letter-spacing: normal;
            word-wrap: normal;
            white-space: nowrap;
            direction: ltr;
            -webkit-font-smoothing: antialiased;
            text-rendering: optimizeLegibility;
            -moz-osx-font-smoothing: grayscale;
            font-feature-settings: 'liga';
        }
    </style>
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="title">图书馆座位预约系统</div>
            <div class="subtitle">微信小程序 + 管理后台原型预览</div>
        </div>

        <div class="section">
            <div class="section-title">微信小程序页面</div>
            <div class="pages-grid">
                <div class="page-card">
                    <div class="page-thumbnail">
                        <iframe src="booking_main.html" frameborder="0"></iframe>
                    </div>
                    <div class="page-info">
                        <div class="page-name">预约主页</div>
                        <div class="page-desc">用户可选择日期、时间段和教室进行预约</div>
                        <div class="page-actions">
                            <a href="booking_main.html" target="_blank" class="page-button view-button">查看页面</a>
                            <a href="#" class="page-button code-button"><i class="icon" style="font-size: 18px;">code</i></a>
                        </div>
                    </div>
                </div>

                <div class="page-card">
                    <div class="page-thumbnail">
                        <iframe src="seat_selection.html" frameborder="0"></iframe>
                    </div>
                    <div class="page-info">
                        <div class="page-name">座位选择页</div>
                        <div class="page-desc">用户可查看座位布局并选择心仪的座位</div>
                        <div class="page-actions">
                            <a href="seat_selection.html" target="_blank" class="page-button view-button">查看页面</a>
                            <a href="#" class="page-button code-button"><i class="icon" style="font-size: 18px;">code</i></a>
                        </div>
                    </div>
                </div>

                <div class="page-card">
                    <div class="page-thumbnail">
                        <iframe src="booking_confirmation.html" frameborder="0"></iframe>
                    </div>
                    <div class="page-info">
                        <div class="page-name">预约确认页</div>
                        <div class="page-desc">用户确认预约信息并提交预约请求</div>
                        <div class="page-actions">
                            <a href="booking_confirmation.html" target="_blank" class="page-button view-button">查看页面</a>
                            <a href="#" class="page-button code-button"><i class="icon" style="font-size: 18px;">code</i></a>
                        </div>
                    </div>
                </div>

                <div class="page-card">
                    <div class="page-thumbnail">
                        <iframe src="booking_success.html" frameborder="0"></iframe>
                    </div>
                    <div class="page-info">
                        <div class="page-name">预约成功页</div>
                        <div class="page-desc">显示预约成功信息和签到二维码</div>
                        <div class="page-actions">
                            <a href="booking_success.html" target="_blank" class="page-button view-button">查看页面</a>
                            <a href="#" class="page-button code-button"><i class="icon" style="font-size: 18px;">code</i></a>
                        </div>
                    </div>
                </div>

                <div class="page-card">
                    <div class="page-thumbnail">
                        <iframe src="check_in.html" frameborder="0"></iframe>
                    </div>
                    <div class="page-info">
                        <div class="page-name">签到页面</div>
                        <div class="page-desc">用户扫码签到、暂离和签退的功能页面</div>
                        <div class="page-actions">
                            <a href="check_in.html" target="_blank" class="page-button view-button">查看页面</a>
                            <a href="#" class="page-button code-button"><i class="icon" style="font-size: 18px;">code</i></a>
                        </div>
                    </div>
                </div>

                <div class="page-card">
                    <div class="page-thumbnail">
                        <iframe src="seat_in_use.html" frameborder="0"></iframe>
                    </div>
                    <div class="page-info">
                        <div class="page-name">座位使用中页面</div>
                        <div class="page-desc">显示座位使用状态、剩余时间和操作按钮</div>
                        <div class="page-actions">
                            <a href="seat_in_use.html" target="_blank" class="page-button view-button">查看页面</a>
                            <a href="#" class="page-button code-button"><i class="icon" style="font-size: 18px;">code</i></a>
                        </div>
                    </div>
                </div>

                <div class="page-card">
                    <div class="page-thumbnail">
                        <iframe src="my_bookings.html" frameborder="0"></iframe>
                    </div>
                    <div class="page-info">
                        <div class="page-name">我的预约列表</div>
                        <div class="page-desc">用户可查看当前和历史预约记录</div>
                        <div class="page-actions">
                            <a href="my_bookings.html" target="_blank" class="page-button view-button">查看页面</a>
                            <a href="#" class="page-button code-button"><i class="icon" style="font-size: 18px;">code</i></a>
                        </div>
                    </div>
                </div>

                <div class="page-card">
                    <div class="page-thumbnail">
                        <iframe src="seat_scan_result.html" frameborder="0"></iframe>
                    </div>
                    <div class="page-info">
                        <div class="page-name">扫码座位页面</div>
                        <div class="page-desc">扫码后查看座位状态和可用时间</div>
                        <div class="page-actions">
                            <a href="seat_scan_result.html" target="_blank" class="page-button view-button">查看页面</a>
                            <a href="#" class="page-button code-button"><i class="icon" style="font-size: 18px;">code</i></a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="section">
            <div class="section-title">管理后台页面</div>
            <div class="pages-grid">
                <div class="page-card">
                    <div class="page-thumbnail">
                        <iframe src="admin_login.html" frameborder="0"></iframe>
                    </div>
                    <div class="page-info">
                        <div class="page-name">管理员登录</div>
                        <div class="page-desc">管理员登录后台的入口页面</div>
                        <div class="page-actions">
                            <a href="admin_login.html" target="_blank" class="page-button view-button">查看页面</a>
                            <a href="#" class="page-button code-button"><i class="icon" style="font-size: 18px;">code</i></a>
                        </div>
                    </div>
                </div>

                <div class="page-card">
                    <div class="page-thumbnail">
                        <iframe src="admin_dashboard.html" frameborder="0"></iframe>
                    </div>
                    <div class="page-info">
                        <div class="page-name">仪表盘</div>
                        <div class="page-desc">显示系统概览和关键数据统计</div>
                        <div class="page-actions">
                            <a href="admin_dashboard.html" target="_blank" class="page-button view-button">查看页面</a>
                            <a href="#" class="page-button code-button"><i class="icon" style="font-size: 18px;">code</i></a>
                        </div>
                    </div>
                </div>

                <div class="page-card">
                    <div class="page-thumbnail">
                        <iframe src="admin_seats.html" frameborder="0"></iframe>
                    </div>
                    <div class="page-info">
                        <div class="page-name">座位管理</div>
                        <div class="page-desc">管理座位布局、状态和属性</div>
                        <div class="page-actions">
                            <a href="admin_seats.html" target="_blank" class="page-button view-button">查看页面</a>
                            <a href="#" class="page-button code-button"><i class="icon" style="font-size: 18px;">code</i></a>
                        </div>
                    </div>
                </div>

                <div class="page-card">
                    <div class="page-thumbnail">
                        <iframe src="admin_bookings.html" frameborder="0"></iframe>
                    </div>
                    <div class="page-info">
                        <div class="page-name">预约记录管理</div>
                        <div class="page-desc">查看和管理所有预约记录</div>
                        <div class="page-actions">
                            <a href="admin_bookings.html" target="_blank" class="page-button view-button">查看页面</a>
                            <a href="#" class="page-button code-button"><i class="icon" style="font-size: 18px;">code</i></a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="flow-section">
            <div class="flow-title">预约流程</div>
            <div class="flow-diagram">
                <div class="flow-steps">
                    <div class="flow-step">
                        <div class="step-icon">
                            <i class="icon">calendar_today</i>
                        </div>
                        <div class="step-name">选择日期和时间</div>
                        <div class="step-desc">用户选择想要预约的日期和时间段</div>
                    </div>
                    
                    <div class="flow-arrow">
                        <i class="icon">arrow_forward</i>
                    </div>
                    
                    <div class="flow-step">
                        <div class="step-icon">
                            <i class="icon">meeting_room</i>
                        </div>
                        <div class="step-name">选择教室</div>
                        <div class="step-desc">用户从可用教室列表中选择</div>
                    </div>
                    
                    <div class="flow-arrow">
                        <i class="icon">arrow_forward</i>
                    </div>
                    
                    <div class="flow-step">
                        <div class="step-icon">
                            <i class="icon">event_seat</i>
                        </div>
                        <div class="step-name">选择座位</div>
                        <div class="step-desc">用户在座位布局图中选择心仪座位</div>
                    </div>
                    
                    <div class="flow-arrow">
                        <i class="icon">arrow_forward</i>
                    </div>
                    
                    <div class="flow-step">
                        <div class="step-icon">
                            <i class="icon">fact_check</i>
                        </div>
                        <div class="step-name">确认预约</div>
                        <div class="step-desc">用户确认预约信息并提交</div>
                    </div>
                    
                    <div class="flow-arrow">
                        <i class="icon">arrow_forward</i>
                    </div>
                    
                    <div class="flow-step">
                        <div class="step-icon">
                            <i class="icon">qr_code_scanner</i>
                        </div>
                        <div class="step-name">扫码签到</div>
                        <div class="step-desc">用户到达图书馆扫码签到使用座位</div>
                    </div>
                </div>
            </div>
        </div>

        <div class="flow-section">
            <div class="flow-title">座位使用流程</div>
            <div class="flow-diagram">
                <div class="flow-steps">
                    <div class="flow-step">
                        <div class="step-icon">
                            <i class="icon">qr_code_scanner</i>
                        </div>
                        <div class="step-name">扫码签到</div>
                        <div class="step-desc">用户扫描座位上的二维码签到</div>
                    </div>
                    
                    <div class="flow-arrow">
                        <i class="icon">arrow_forward</i>
                    </div>
                    
                    <div class="flow-step">
                        <div class="step-icon">
                            <i class="icon">chair</i>
                        </div>
                        <div class="step-name">使用座位</div>
                        <div class="step-desc">用户正常使用座位</div>
                    </div>
                    
                    <div class="flow-arrow">
                        <i class="icon">arrow_forward</i>
                    </div>
                    
                    <div class="flow-step">
                        <div class="step-icon">
                            <i class="icon">timer</i>
                        </div>
                        <div class="step-name">临时暂离</div>
                        <div class="step-desc">用户可临时暂离不超过30分钟</div>
                    </div>
                    
                    <div class="flow-arrow">
                        <i class="icon">arrow_forward</i>
                    </div>
                    
                    <div class="flow-step">
                        <div class="step-icon">
                            <i class="icon">qr_code_scanner</i>
                        </div>
                        <div class="step-name">返回签到</div>
                        <div class="step-desc">暂离后返回需再次扫码</div>
                    </div>
                    
                    <div class="flow-arrow">
                        <i class="icon">arrow_forward</i>
                    </div>
                    
                    <div class="flow-step">
                        <div class="step-icon">
                            <i class="icon">logout</i>
                        </div>
                        <div class="step-name">签退</div>
                        <div class="step-desc">用户使用完毕后签退释放座位</div>
                    </div>
                </div>
            </div>
        </div>

        <div class="flow-section">
            <div class="flow-title">散座使用流程</div>
            <div class="flow-diagram">
                <div class="flow-steps">
                    <div class="flow-step">
                        <div class="step-icon">
                            <i class="icon">search</i>
                        </div>
                        <div class="step-name">发现散座</div>
                        <div class="step-desc">用户发现有空闲的散座</div>
                    </div>
                    
                    <div class="flow-arrow">
                        <i class="icon">arrow_forward</i>
                    </div>
                    
                    <div class="flow-step">
                        <div class="step-icon">
                            <i class="icon">qr_code_scanner</i>
                        </div>
                        <div class="step-name">扫码查看</div>
                        <div class="step-desc">扫描座位二维码查看状态</div>
                    </div>
                    
                    <div class="flow-arrow">
                        <i class="icon">arrow_forward</i>
                    </div>
                    
                    <div class="flow-step">
                        <div class="step-icon">
                            <i class="icon">check_circle</i>
                        </div>
                        <div class="step-name">确认使用</div>
                        <div class="step-desc">确认使用散座直到当前时间段结束</div>
                    </div>
                    
                    <div class="flow-arrow">
                        <i class="icon">arrow_forward</i>
                    </div>
                    
                    <div class="flow-step">
                        <div class="step-icon">
                            <i class="icon">chair</i>
                        </div>
                        <div class="step-name">使用座位</div>
                        <div class="step-desc">用户正常使用座位</div>
                    </div>
                    
                    <div class="flow-arrow">
                        <i class="icon">arrow_forward</i>
                    </div>
                    
                    <div class="flow-step">
                        <div class="step-icon">
                            <i class="icon">logout</i>
                        </div>
                        <div class="step-name">签退</div>
                        <div class="step-desc">用户使用完毕后签退</div>
                    </div>
                </div>
            </div>
        </div>

        <div class="footer">
            <p>图书馆座位预约系统 - 原型设计 © 2023</p>
            <p>设计文档请查看: <a href="doc/图书馆座位预约系统应用流程与需求文档.md" target="_blank">需求文档</a></p>
        </div>
    </div>
</body>
</html>
