<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>座位详情 - 图书馆座位预约系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            -webkit-tap-highlight-color: transparent;
            font-family: "PingFang SC", "Helvetica Neue", Arial, sans-serif;
        }

        body {
            background-color: #f8f8f8;
            color: #333;
            font-size: 14px;
            line-height: 1.5;
            overflow-x: hidden;
        }

        /* 模拟iPhone 15 Pro Max */
        .device-container {
            width: 100%;
            max-width: 430px;
            height: 932px;
            margin: 20px auto;
            position: relative;
            overflow: hidden;
            border-radius: 55px;
            box-shadow: 0 20px 50px rgba(0, 0, 0, 0.3);
            background: #1a1a1a;
            padding: 12px;
        }

        /* 刘海区域 */
        .notch {
            position: absolute;
            width: 160px;
            height: 34px;
            background: #1a1a1a;
            top: 12px;
            left: 50%;
            transform: translateX(-50%);
            border-radius: 0 0 20px 20px;
            z-index: 100;
        }

        /* 底部指示条 */
        .home-indicator {
            position: absolute;
            width: 140px;
            height: 5px;
            background: #ffffff;
            bottom: 25px;
            left: 50%;
            transform: translateX(-50%);
            border-radius: 3px;
            z-index: 100;
        }

        /* 手机屏幕 */
        .screen {
            width: 100%;
            height: 100%;
            background: #ffffff;
            border-radius: 45px;
            overflow: hidden;
            position: relative;
        }

        /* 小程序内容区域 */
        .app-container {
            width: 100%;
            height: 100%;
            overflow: hidden;
            display: flex;
            flex-direction: column;
        }

        /* 顶部导航栏 */
        .nav-bar {
            height: 88px;
            background-color: #b1030d;
            color: white;
            display: flex;
            justify-content: space-between;
            align-items: flex-end;
            padding: 0 16px 10px;
            position: relative;
            z-index: 10;
        }

        .nav-left {
            font-size: 24px;
            padding-bottom: 10px;
        }

        .nav-title {
            font-size: 18px;
            font-weight: 600;
            padding-bottom: 10px;
        }

        .nav-right {
            width: 24px;
            padding-bottom: 10px;
        }

        /* 主内容区 */
        .main-content {
            flex: 1;
            overflow-y: auto;
            -webkit-overflow-scrolling: touch;
            padding: 16px;
            padding-bottom: 70px;
        }

        /* 座位状态卡片 */
        .seat-status-card {
            background: white;
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            animation: fadeIn 0.5s ease forwards;
        }

        .seat-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
            padding-bottom: 16px;
            border-bottom: 1px solid #f0f0f0;
        }

        .seat-title {
            font-size: 18px;
            font-weight: 600;
            color: #333;
        }

        .seat-id {
            font-size: 16px;
            font-weight: 600;
            color: #b1030d;
        }

        .seat-info-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 16px;
            margin-bottom: 16px;
        }

        .seat-info-item {
            display: flex;
            flex-direction: column;
        }

        .info-label {
            font-size: 13px;
            color: #999;
            margin-bottom: 4px;
        }

        .info-value {
            font-size: 15px;
            color: #333;
            font-weight: 500;
        }

        /* 座位状态标签 */
        .status-badge {
            display: inline-block;
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: 500;
            margin-top: 8px;
            text-align: center;
        }

        .status-available {
            background: #e8f5e9;
            color: #4CAF50;
        }

        .status-occupied {
            background: #ffebee;
            color: #f44336;
        }

        .status-reserved {
            background: #e3f2fd;
            color: #2196F3;
        }

        .status-temp-leave {
            background: #fff8e1;
            color: #FFC107;
        }

        .status-malfunction {
            background: #f5f5f5;
            color: #9e9e9e;
        }

        /* 座位特点 */
        .seat-features {
            display: flex;
            flex-wrap: wrap;
            margin-top: 16px;
        }

        .seat-feature {
            display: flex;
            align-items: center;
            margin-right: 16px;
            margin-bottom: 8px;
            font-size: 13px;
            color: #666;
        }

        .seat-feature i {
            margin-right: 4px;
            color: #b1030d;
            font-size: 16px;
        }

        /* 查询卡片 */
        .query-card {
            background: white;
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            animation: fadeIn 0.5s ease forwards;
            animation-delay: 0.1s;
            opacity: 0;
        }

        .query-title {
            font-size: 16px;
            font-weight: 600;
            color: #333;
            margin-bottom: 16px;
        }

        .query-form {
            display: flex;
            flex-direction: column;
        }

        .form-group {
            margin-bottom: 16px;
        }

        .form-label {
            font-size: 14px;
            color: #666;
            margin-bottom: 8px;
        }

        .form-input {
            width: 100%;
            height: 44px;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 0 12px;
            font-size: 14px;
        }

        .form-select {
            width: 100%;
            height: 44px;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 0 12px;
            font-size: 14px;
            background: white;
        }

        .query-button {
            height: 44px;
            background: #b1030d;
            color: white;
            border: none;
            border-radius: 22px;
            font-size: 16px;
            font-weight: 500;
            margin-top: 8px;
        }

        /* 可用时间卡片 */
        .available-times-card {
            background: white;
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            animation: fadeIn 0.5s ease forwards;
            animation-delay: 0.2s;
            opacity: 0;
            display: none;
        }

        .available-times-title {
            font-size: 16px;
            font-weight: 600;
            color: #333;
            margin-bottom: 16px;
        }

        .time-slots {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 10px;
        }

        .time-slot {
            height: 60px;
            border-radius: 8px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            transition: all 0.3s ease;
        }

        .time-slot.available {
            background: #e8f5e9;
            color: #4CAF50;
            border: 1px solid #4CAF50;
        }

        .time-slot.unavailable {
            background: #f5f5f5;
            color: #9e9e9e;
            border: 1px solid #ddd;
        }

        .time-range {
            font-weight: 600;
            margin-bottom: 4px;
        }

        .time-status {
            font-size: 12px;
        }

        /* 操作按钮 */
        .action-buttons {
            display: grid;
            grid-template-columns: 1fr;
            gap: 12px;
            margin-bottom: 20px;
            animation: fadeIn 0.5s ease forwards;
            animation-delay: 0.3s;
            opacity: 0;
        }

        .action-button {
            height: 50px;
            border-radius: 25px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
            font-weight: 600;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
        }

        .primary-btn {
            background: #b1030d;
            color: white;
        }

        .secondary-btn {
            background: white;
            color: #b1030d;
            border: 1px solid #b1030d;
        }

        .disabled-btn {
            background: #f5f5f5;
            color: #999;
            border: none;
        }

        .action-button i {
            margin-right: 8px;
            font-size: 20px;
        }

        /* 底部导航 */
        .tab-bar {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: white;
            display: flex;
            box-shadow: 0 -1px 10px rgba(0, 0, 0, 0.05);
            z-index: 100;
        }

        .tab-item {
            flex: 1;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            color: #999;
            font-size: 10px;
            transition: all 0.3s ease;
        }

        .tab-item.active {
            color: #b1030d;
        }

        .tab-icon {
            font-size: 24px;
            margin-bottom: 2px;
        }

        /* 图标字体 */
        .icon {
            font-family: "Material Icons";
            font-weight: normal;
            font-style: normal;
            font-size: 24px;
            display: inline-block;
            line-height: 1;
            text-transform: none;
            letter-spacing: normal;
            word-wrap: normal;
            white-space: nowrap;
            direction: ltr;
            -webkit-font-smoothing: antialiased;
            text-rendering: optimizeLegibility;
            -moz-osx-font-smoothing: grayscale;
            font-feature-settings: 'liga';
        }

        /* 动画效果 */
        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* 模态框 */
        .modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1000;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s;
        }

        .modal.show {
            opacity: 1;
            visibility: visible;
        }

        .modal-content {
            width: 80%;
            max-width: 320px;
            background: white;
            border-radius: 16px;
            padding: 24px;
            text-align: center;
            transform: translateY(20px);
            transition: all 0.3s;
        }

        .modal.show .modal-content {
            transform: translateY(0);
        }

        .modal-icon {
            width: 60px;
            height: 60px;
            border-radius: 30px;
            background: #e8f5e9;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 16px;
            font-size: 30px;
            color: #4CAF50;
        }

        .modal-title {
            font-size: 18px;
            font-weight: 600;
            color: #333;
            margin-bottom: 8px;
        }

        .modal-desc {
            font-size: 14px;
            color: #666;
            margin-bottom: 20px;
        }

        .modal-button {
            height: 44px;
            border-radius: 22px;
            background: #b1030d;
            color: white;
            font-size: 16px;
            font-weight: 600;
            display: flex;
            align-items: center;
            justify-content: center;
            border: none;
            width: 100%;
        }
    </style>
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
</head>
<body>
    <div class="device-container">
        <div class="notch"></div>
        <div class="home-indicator"></div>
        <div class="screen">
            <div class="app-container">
                <!-- 顶部导航栏 -->
                <div class="nav-bar">
                    <div class="nav-left">
                        <i class="icon" onclick="goBack()">arrow_back</i>
                    </div>
                    <div class="nav-title">座位详情</div>
                    <div class="nav-right"></div>
                </div>

                <!-- 主内容区 -->
                <div class="main-content">
                    <!-- 座位状态卡片 -->
                    <div class="seat-status-card">
                        <div class="seat-header">
                            <div class="seat-title">A101 自习室</div>
                            <div class="seat-id">A5</div>
                        </div>
                        <div class="seat-info-grid">
                            <div class="seat-info-item">
                                <div class="info-label">当前状态</div>
                                <div class="info-value">
                                    <div class="status-badge status-available" id="seatStatus">空闲</div>
                                </div>
                            </div>
                            <div class="seat-info-item">
                                <div class="info-label">位置</div>
                                <div class="info-value">图书馆1楼</div>
                            </div>
                        </div>
                        <div class="seat-features">
                            <div class="seat-feature">
                                <i class="icon">window</i> 靠窗
                            </div>
                            <div class="seat-feature">
                                <i class="icon">power</i> 有电源
                            </div>
                            <div class="seat-feature">
                                <i class="icon">visibility</i> 视野好
                            </div>
                            <div class="seat-feature">
                                <i class="icon">volume_off</i> 安静区
                            </div>
                        </div>
                    </div>

                    <!-- 查询卡片 -->
                    <div class="query-card">
                        <div class="query-title">查询可用时间</div>
                        <div class="query-form">
                           复用首页的选择日期和选择时间，默认是当天，已被占用的时间段为不可选
                        </div>
                    </div>

                    <!-- 可用时间卡片 -->
                    <div class="available-times-card" id="availableTimesCard">
                        <div class="available-times-title">2023年5月17日可用时间</div>
                        <div class="time-slots">
                            <div class="time-slot available">
                                <div class="time-range">07:00-12:00</div>
                                <div class="time-status">可预约</div>
                            </div>
                            <div class="time-slot unavailable">
                                <div class="time-range">12:00-17:00</div>
                                <div class="time-status">已预约</div>
                            </div>
                            <div class="time-slot available">
                                <div class="time-range">17:00-22:00</div>
                                <div class="time-status">可预约</div>
                            </div>
                        </div>
                    </div>

                    <!-- 操作按钮 -->
                    <div class="action-buttons">
                        <button class="action-button primary-btn" id="mainActionBtn" onclick="handleMainAction()">
                            <i class="icon">event_seat</i> 立即预约
                        </button>
                        <!-- <button class="action-button secondary-btn" onclick="reportIssue()">
                            <i class="icon">report_problem</i> 报告故障
                        </button> -->
                    </div>
                </div>

                <!-- 底部导航栏 -->
                <div class="tab-bar">
                    <div class="tab-item active">
                        <i class="icon tab-icon">event_seat</i>
                        <div>预约</div>
                    </div>
                    <div class="tab-item">
                        <i class="icon tab-icon">schedule</i>
                        <div>我的座位</div>
                    </div>
                    <div class="tab-item">
                        <i class="icon tab-icon">person</i>
                        <div>我的</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 报告故障模态框 -->
    <div class="modal" id="reportModal">
        <div class="modal-content">
            <div class="modal-icon" style="background: #ffebee; color: #f44336;">
                <i class="icon">report_problem</i>
            </div>
            <div class="modal-title">报告座位故障</div>
            <div class="modal-desc">您确定要报告此座位存在故障吗？工作人员将尽快处理。</div>
            <button class="modal-button" onclick="confirmReport()">确认报告</button>
        </div>
    </div>

    <!-- 预约成功模态框 -->
    <div class="modal" id="successModal">
        <div class="modal-content">
            <div class="modal-icon">
                <i class="icon">check_circle</i>
            </div>
            <div class="modal-title">预约成功</div>
            <div class="modal-desc">您已成功预约A101自习室A5座位<br>请在预约时间内到达并签到</div>
            <button class="modal-button" onclick="goToBookingDetail()">查看预约详情</button>
        </div>
    </div>

    <script>
        // 当前座位状态
        let currentStatus = 'available'; // available, occupied, reserved, temp_leave, malfunction
        
        // 更新座位状态显示
        function updateSeatStatus(status) {
            const statusBadge = document.getElementById('seatStatus');
            const mainActionBtn = document.getElementById('mainActionBtn');
            
            // 移除所有状态类
            statusBadge.classList.remove('status-available', 'status-occupied', 'status-reserved', 'status-temp-leave', 'status-malfunction');
            
            // 根据状态更新显示
            if (status === 'available') {
                statusBadge.classList.add('status-available');
                statusBadge.textContent = '空闲';
                mainActionBtn.textContent = '立即预约';
                mainActionBtn.className = 'action-button primary-btn';
                mainActionBtn.innerHTML = '<i class="icon">event_seat</i> 立即预约';
            } else if (status === 'occupied') {
                statusBadge.classList.add('status-occupied');
                statusBadge.textContent = '使用中';
                mainActionBtn.className = 'action-button disabled-btn';
                mainActionBtn.innerHTML = '<i class="icon">block</i> 不可预约';
            } else if (status === 'reserved') {
                statusBadge.classList.add('status-reserved');
                statusBadge.textContent = '已预约';
                mainActionBtn.className = 'action-button disabled-btn';
                mainActionBtn.innerHTML = '<i class="icon">block</i> 不可预约';
            } else if (status === 'temp_leave') {
                statusBadge.classList.add('status-temp-leave');
                statusBadge.textContent = '暂离中';
                mainActionBtn.className = 'action-button disabled-btn';
                mainActionBtn.innerHTML = '<i class="icon">block</i> 不可预约';
            } else if (status === 'malfunction') {
                statusBadge.classList.add('status-malfunction');
                statusBadge.textContent = '故障';
                mainActionBtn.className = 'action-button disabled-btn';
                mainActionBtn.innerHTML = '<i class="icon">block</i> 不可预约';
            }
            
            currentStatus = status;
        }
        
        // 查询座位可用性
        function queryAvailability() {
            const date = document.getElementById('queryDate').value;
            const timeSlot = document.getElementById('queryTimeSlot').value;
            
            // 显示查询结果卡片
            document.getElementById('availableTimesCard').style.display = 'block';
            
            // 更新标题
            document.querySelector('.available-times-title').textContent = `${date.replace(/-/g, '年')}月日可用时间`;
            
            // 实际应用中应该从服务器获取数据
            console.log('查询日期:', date, '时间段:', timeSlot);
        }
        
        // 主操作按钮处理
        function handleMainAction() {
            if (currentStatus === 'available') {
                // 如果座位空闲，显示预约成功模态框
                document.getElementById('successModal').classList.add('show');
            } else {
                // 其他状态不可操作
                alert('当前座位不可预约');
            }
        }
        
        // 报告故障
        function reportIssue() {
            document.getElementById('reportModal').classList.add('show');
        }
        
        // 确认报告故障
        function confirmReport() {
            // 关闭模态框
            document.getElementById('reportModal').classList.remove('show');
            
            // 更新座位状态为故障
            updateSeatStatus('malfunction');
            
            // 提示用户
            setTimeout(() => {
                alert('感谢您的反馈，工作人员将尽快处理');
            }, 300);
        }
        
        // 前往预约详情
        function goToBookingDetail() {
            // 关闭模态框
            document.getElementById('successModal').classList.remove('show');
            
            // 实际应用中应该跳转到预约详情页面
            alert('跳转到预约详情页面');
        }
        
        // 返回上一页
        function goBack() {
            // 实际应用中应该使用微信小程序的导航API
            alert('返回上一页');
            // wx.navigateBack();
        }
        
        // 模拟不同状态
        function simulateDifferentStatus() {
            const statuses = ['available', 'occupied', 'reserved', 'temp_leave', 'malfunction'];
            let index = 0;
            
            // 每3秒切换一次状态，用于演示
            setInterval(() => {
                updateSeatStatus(statuses[index]);
                index = (index + 1) % statuses.length;
            }, 3000);
        }
        
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化座位状态
            updateSeatStatus('available');
            
            // 如果需要演示不同状态，取消下面的注释
            // simulateDifferentStatus();
        });
    </script>
</body>
</html>
