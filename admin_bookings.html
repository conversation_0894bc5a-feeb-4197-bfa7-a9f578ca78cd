<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>预约记录管理 - 图书馆座位预约系统</title>
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: "PingFang SC", "Helvetica Neue", Arial, sans-serif;
        }

        body {
            background-color: #f5f5f5;
            color: #333;
            font-size: 14px;
            line-height: 1.5;
            display: flex;
            min-height: 100vh;
        }

        /* 侧边栏样式 */
        .sidebar {
            width: 240px;
            background: #2c3e50;
            color: white;
            height: 100vh;
            position: fixed;
            left: 0;
            top: 0;
            overflow-y: auto;
        }

        .sidebar-header {
            padding: 20px;
            text-align: center;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .sidebar-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 5px;
        }

        .sidebar-subtitle {
            font-size: 12px;
            opacity: 0.7;
        }

        .sidebar-menu {
            padding: 20px 0;
        }

        .menu-item {
            padding: 12px 20px;
            display: flex;
            align-items: center;
            color: rgba(255, 255, 255, 0.7);
            text-decoration: none;
            transition: all 0.3s;
        }

        .menu-item:hover {
            background: rgba(255, 255, 255, 0.1);
            color: white;
        }

        .menu-item.active {
            background: #b1030d;
            color: white;
        }

        .menu-icon {
            margin-right: 10px;
            font-size: 20px;
        }

        .menu-text {
            font-size: 14px;
        }

        /* 主内容区样式 */
        .main-content {
            flex: 1;
            margin-left: 240px;
            padding: 20px;
        }

        /* 顶部导航栏 */
        .top-nav {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 20px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            margin-bottom: 20px;
        }

        .page-title {
            font-size: 20px;
            font-weight: 600;
            color: #333;
        }

        .user-info {
            display: flex;
            align-items: center;
        }

        .user-name {
            margin-right: 10px;
            font-weight: 500;
        }

        .user-avatar {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: #b1030d;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
        }

        /* 筛选和操作栏 */
        .filter-bar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 20px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            margin-bottom: 20px;
        }

        .filter-group {
            display: flex;
            align-items: center;
            flex-wrap: wrap;
        }

        .filter-label {
            margin-right: 10px;
            font-weight: 500;
        }

        .filter-select {
            height: 36px;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 0 10px;
            margin-right: 15px;
            margin-bottom: 5px;
        }

        .action-button {
            height: 36px;
            padding: 0 15px;
            background: #b1030d;
            color: white;
            border: none;
            border-radius: 4px;
            font-weight: 500;
            cursor: pointer;
            display: flex;
            align-items: center;
        }

        .action-button .material-icons {
            margin-right: 5px;
            font-size: 18px;
        }

        /* 预约记录卡片 */
        .bookings-card {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            overflow: hidden;
            margin-bottom: 20px;
        }

        .card-header {
            padding: 15px 20px;
            border-bottom: 1px solid #f0f0f0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .card-title {
            font-size: 16px;
            font-weight: 600;
        }

        .card-actions {
            display: flex;
        }

        .card-action {
            margin-left: 10px;
            color: #777;
            cursor: pointer;
        }

        .card-action:hover {
            color: #b1030d;
        }

        /* 预约记录表格 */
        .bookings-table {
            width: 100%;
            border-collapse: collapse;
        }

        .bookings-table th,
        .bookings-table td {
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid #f0f0f0;
        }

        .bookings-table th {
            font-weight: 600;
            color: #555;
            background: #f9f9f9;
        }

        .bookings-table tr:hover {
            background: #f9f9f9;
        }

        .booking-status {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
        }

        .status-waiting {
            background: #e3f2fd;
            color: #2196F3;
        }

        .status-checked-in {
            background: #e8f5e9;
            color: #4CAF50;
        }

        .status-temp-leave {
            background: #fff8e1;
            color: #FFC107;
        }

        .status-completed {
            background: #f5f5f5;
            color: #9e9e9e;
        }

        .status-cancelled {
            background: #ffebee;
            color: #f44336;
        }

        .status-violated {
            background: #f8bbd0;
            color: #e91e63;
        }

        .booking-actions {
            display: flex;
        }

        .booking-action {
            margin-right: 10px;
            color: #777;
            cursor: pointer;
        }

        .booking-action:hover {
            color: #b1030d;
        }

        /* 分页 */
        .pagination {
            display: flex;
            justify-content: flex-end;
            padding: 15px 20px;
            border-top: 1px solid #f0f0f0;
        }

        .page-item {
            width: 32px;
            height: 32px;
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 5px;
            cursor: pointer;
        }

        .page-item.active {
            background: #b1030d;
            color: white;
        }

        /* 预约详情模态框 */
        .modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1000;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s;
        }

        .modal.show {
            opacity: 1;
            visibility: visible;
        }

        .modal-content {
            width: 500px;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            transform: translateY(20px);
            transition: all 0.3s;
        }

        .modal.show .modal-content {
            transform: translateY(0);
        }

        .modal-header {
            padding: 15px 20px;
            border-bottom: 1px solid #f0f0f0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-title {
            font-size: 18px;
            font-weight: 600;
        }

        .modal-close {
            cursor: pointer;
            color: #777;
        }

        .modal-body {
            padding: 20px;
        }

        .detail-group {
            margin-bottom: 15px;
        }

        .detail-label {
            font-weight: 500;
            margin-bottom: 5px;
            color: #777;
        }

        .detail-value {
            font-size: 16px;
        }

        .detail-status {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
            margin-left: 10px;
        }

        .detail-timeline {
            margin-top: 20px;
            border-left: 2px solid #f0f0f0;
            padding-left: 20px;
        }

        .timeline-item {
            position: relative;
            padding-bottom: 20px;
        }

        .timeline-item:last-child {
            padding-bottom: 0;
        }

        .timeline-item:before {
            content: '';
            position: absolute;
            left: -26px;
            top: 0;
            width: 10px;
            height: 10px;
            border-radius: 50%;
            background: #b1030d;
        }

        .timeline-time {
            font-size: 12px;
            color: #777;
            margin-bottom: 5px;
        }

        .timeline-content {
            font-size: 14px;
        }

        .modal-footer {
            padding: 15px 20px;
            border-top: 1px solid #f0f0f0;
            display: flex;
            justify-content: flex-end;
        }

        .modal-button {
            height: 36px;
            padding: 0 15px;
            border-radius: 4px;
            font-weight: 500;
            cursor: pointer;
            margin-left: 10px;
        }

        .button-cancel {
            background: #f5f5f5;
            color: #333;
            border: none;
        }

        .button-confirm {
            background: #b1030d;
            color: white;
            border: none;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .sidebar {
                width: 70px;
            }
            
            .sidebar-title, .sidebar-subtitle, .menu-text {
                display: none;
            }
            
            .menu-icon {
                margin-right: 0;
            }
            
            .main-content {
                margin-left: 70px;
            }
            
            .filter-bar {
                flex-direction: column;
                align-items: flex-start;
            }
            
            .filter-group {
                margin-bottom: 10px;
                width: 100%;
            }
            
            .action-button {
                margin-top: 10px;
            }
            
            .bookings-table {
                display: block;
                overflow-x: auto;
            }
        }
    </style>
</head>
<body>
    <!-- 侧边栏 -->
    <div class="sidebar">
        <div class="sidebar-header">
            <div class="sidebar-title">图书馆座位预约系统</div>
            <div class="sidebar-subtitle">管理后台</div>
        </div>
        <div class="sidebar-menu">
            <a href="admin_dashboard.html" class="menu-item">
                <span class="material-icons menu-icon">dashboard</span>
                <span class="menu-text">仪表盘</span>
            </a>
            <a href="admin_seats.html" class="menu-item">
                <span class="material-icons menu-icon">event_seat</span>
                <span class="menu-text">座位管理</span>
            </a>
            <a href="admin_rooms.html" class="menu-item">
                <span class="material-icons menu-icon">meeting_room</span>
                <span class="menu-text">教室管理</span>
            </a>
            <a href="admin_bookings.html" class="menu-item active">
                <span class="material-icons menu-icon">book_online</span>
                <span class="menu-text">预约记录</span>
            </a>
            <a href="admin_violations.html" class="menu-item">
                <span class="material-icons menu-icon">gavel</span>
                <span class="menu-text">违约记录</span>
            </a>
            <a href="admin_users.html" class="menu-item">
                <span class="material-icons menu-icon">people</span>
                <span class="menu-text">用户管理</span>
            </a>
            <a href="admin_settings.html" class="menu-item">
                <span class="material-icons menu-icon">settings</span>
                <span class="menu-text">系统设置</span>
            </a>
            <a href="admin_login.html" class="menu-item">
                <span class="material-icons menu-icon">logout</span>
                <span class="menu-text">退出登录</span>
            </a>
        </div>
    </div>

    <!-- 主内容区 -->
    <div class="main-content">
        <!-- 顶部导航栏 -->
        <div class="top-nav">
            <div class="page-title">预约记录管理</div>
            <div class="user-info">
                <span class="user-name">管理员</span>
                <div class="user-avatar">A</div>
            </div>
        </div>

        <!-- 筛选和操作栏 -->
        <div class="filter-bar">
            <div class="filter-group">
                <label class="filter-label">状态:</label>
                <select class="filter-select">
                    <option value="">全部状态</option>
                    <option value="waiting">待签到</option>
                    <option value="checked-in">已签到</option>
                    <option value="temp-leave">暂离中</option>
                    <option value="completed">已完成</option>
                    <option value="cancelled">已取消</option>
                    <option value="violated">违约</option>
                </select>
                
                <label class="filter-label">教室:</label>
                <select class="filter-select">
                    <option value="">全部教室</option>
                    <option value="A101">A101 自习室</option>
                    <option value="A102">A102 电子阅览室</option>
                    <option value="B201">B201 安静自习室</option>
                    <option value="B202">B202 研讨室</option>
                    <option value="C301">C301 图书阅览室</option>
                </select>
                
                <label class="filter-label">日期:</label>
                <input type="date" class="filter-select" value="2023-05-17">
                
                <label class="filter-label">时间段:</label>
                <select class="filter-select">
                    <option value="">全部时间段</option>
                    <option value="morning">07:00-12:00</option>
                    <option value="afternoon">12:00-17:00</option>
                    <option value="evening">17:00-22:00</option>
                </select>
                
                <label class="filter-label">学号:</label>
                <input type="text" class="filter-select" placeholder="输入学号">
            </div>
            
            <button class="action-button" onclick="exportBookings()">
                <span class="material-icons">file_download</span> 导出记录
            </button>
        </div>

        <!-- 预约记录卡片 -->
        <div class="bookings-card">
            <div class="card-header">
                <div class="card-title">预约记录列表</div>
                <div class="card-actions">
                    <span class="material-icons card-action" title="刷新">refresh</span>
                    <span class="material-icons card-action" title="打印">print</span>
                </div>
            </div>
            <table class="bookings-table">
                <thead>
                    <tr>
                        <th>预约ID</th>
                        <th>学生信息</th>
                        <th>教室/座位</th>
                        <th>日期</th>
                        <th>时间段</th>
                        <th>状态</th>
                        <th>创建时间</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>B20230517001</td>
                        <td>张三 (2020001)</td>
                        <td>A101 / A2</td>
                        <td>2023-05-17</td>
                        <td>12:00-17:00</td>
                        <td><span class="booking-status status-checked-in">已签到</span></td>
                        <td>2023-05-16 10:05</td>
                        <td class="booking-actions">
                            <span class="material-icons booking-action" title="查看详情" onclick="showBookingDetail('B20230517001')">visibility</span>
                            <span class="material-icons booking-action" title="强制签退">logout</span>
                        </td>
                    </tr>
                    <tr>
                        <td>B20230517002</td>
                        <td>李四 (2020002)</td>
                        <td>B201 / C8</td>
                        <td>2023-05-18</td>
                        <td>07:00-12:00</td>
                        <td><span class="booking-status status-waiting">待签到</span></td>
                        <td>2023-05-16 14:23</td>
                        <td class="booking-actions">
                            <span class="material-icons booking-action" title="查看详情" onclick="showBookingDetail('B20230517002')">visibility</span>
                            <span class="material-icons booking-action" title="取消预约">cancel</span>
                        </td>
                    </tr>
                    <tr>
                        <td>B20230517003</td>
                        <td>王五 (2020003)</td>
                        <td>C301 / D12</td>
                        <td>2023-05-17</td>
                        <td>17:00-22:00</td>
                        <td><span class="booking-status status-temp-leave">暂离中</span></td>
                        <td>2023-05-16 09:17</td>
                        <td class="booking-actions">
                            <span class="material-icons booking-action" title="查看详情" onclick="showBookingDetail('B20230517003')">visibility</span>
                            <span class="material-icons booking-action" title="强制签退">logout</span>
                        </td>
                    </tr>
                    <tr>
                        <td>B20230517004</td>
                        <td>赵六 (2020004)</td>
                        <td>A102 / B7</td>
                        <td>2023-05-17</td>
                        <td>07:00-12:00</td>
                        <td><span class="booking-status status-completed">已完成</span></td>
                        <td>2023-05-16 08:45</td>
                        <td class="booking-actions">
                            <span class="material-icons booking-action" title="查看详情" onclick="showBookingDetail('B20230517004')">visibility</span>
                        </td>
                    </tr>
                    <tr>
                        <td>B20230517005</td>
                        <td>孙七 (2020005)</td>
                        <td>B202 / E3</td>
                        <td>2023-05-17</td>
                        <td>12:00-17:00</td>
                        <td><span class="booking-status status-cancelled">已取消</span></td>
                        <td>2023-05-16 11:30</td>
                        <td class="booking-actions">
                            <span class="material-icons booking-action" title="查看详情" onclick="showBookingDetail('B20230517005')">visibility</span>
                        </td>
                    </tr>
                    <tr>
                        <td>B20230517006</td>
                        <td>周八 (2020006)</td>
                        <td>A101 / A9</td>
                        <td>2023-05-17</td>
                        <td>07:00-12:00</td>
                        <td><span class="booking-status status-violated">违约</span></td>
                        <td>2023-05-16 10:15</td>
                        <td class="booking-actions">
                            <span class="material-icons booking-action" title="查看详情" onclick="showBookingDetail('B20230517006')">visibility</span>
                            <span class="material-icons booking-action" title="处理违约">gavel</span>
                        </td>
                    </tr>
                </tbody>
            </table>
            <div class="pagination">
                <div class="page-item active">1</div>
                <div class="page-item">2</div>
                <div class="page-item">3</div>
                <div class="page-item">4</div>
                <div class="page-item">5</div>
                <div class="page-item">
                    <span class="material-icons">chevron_right</span>
                </div>
            </div>
        </div>
    </div>

    <!-- 预约详情模态框 -->
    <div class="modal" id="bookingDetailModal">
        <div class="modal-content">
            <div class="modal-header">
                <div class="modal-title">预约详情</div>
                <span class="material-icons modal-close" onclick="closeModal()">close</span>
            </div>
            <div class="modal-body">
                <div class="detail-group">
                    <div class="detail-label">预约ID</div>
                    <div class="detail-value">B20230517001</div>
                </div>
                <div class="detail-group">
                    <div class="detail-label">学生信息</div>
                    <div class="detail-value">张三 (2020001) - 计算机科学与技术学院</div>
                </div>
                <div class="detail-group">
                    <div class="detail-label">教室/座位</div>
                    <div class="detail-value">A101 自习室 / A2 (靠窗, 有电源)</div>
                </div>
                <div class="detail-group">
                    <div class="detail-label">日期和时间段</div>
                    <div class="detail-value">2023年5月17日 12:00-17:00</div>
                </div>
                <div class="detail-group">
                    <div class="detail-label">状态</div>
                    <div class="detail-value">
                        已签到
                        <span class="detail-status status-checked-in">已签到</span>
                    </div>
                </div>
                <div class="detail-group">
                    <div class="detail-label">创建时间</div>
                    <div class="detail-value">2023-05-16 10:05:23</div>
                </div>
                
                <div class="detail-timeline">
                    <div class="timeline-item">
                        <div class="timeline-time">2023-05-16 10:05:23</div>
                        <div class="timeline-content">创建预约</div>
                    </div>
                    <div class="timeline-item">
                        <div class="timeline-time">2023-05-17 12:05:47</div>
                        <div class="timeline-content">签到成功</div>
                    </div>
                    <div class="timeline-item">
                        <div class="timeline-time">2023-05-17 14:30:12</div>
                        <div class="timeline-content">暂离</div>
                    </div>
                    <div class="timeline-item">
                        <div class="timeline-time">2023-05-17 14:55:36</div>
                        <div class="timeline-content">返回座位</div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button class="modal-button button-cancel" onclick="closeModal()">关闭</button>
                <button class="modal-button button-confirm" onclick="forceCheckout()">强制签退</button>
            </div>
        </div>
    </div>

    <script>
        // 显示预约详情模态框
        function showBookingDetail(bookingId) {
            document.getElementById('bookingDetailModal').classList.add('show');
            // 实际应用中应该根据bookingId加载对应的预约详情
            console.log('查看预约详情:', bookingId);
        }

        // 关闭模态框
        function closeModal() {
            document.getElementById('bookingDetailModal').classList.remove('show');
        }

        // 强制签退
        function forceCheckout() {
            alert('已强制签退该预约');
            closeModal();
        }

        // 导出预约记录
        function exportBookings() {
            alert('预约记录导出功能将在此实现');
        }
    </script>
</body>
</html>
