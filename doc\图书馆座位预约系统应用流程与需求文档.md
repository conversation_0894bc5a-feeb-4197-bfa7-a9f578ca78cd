# 图书馆座位预约系统应用流程文档与需求文档

## 1. 项目概述

### 1.1 项目背景
大学图书馆座位资源有限，学生占座现象严重，导致座位资源利用率低下。本系统旨在通过软件预约与硬件门禁闸机、课桌墨水屏相结合的方式，实现座位的高效管理与分配，解决占座问题，提高座位利用率。

### 1.2 项目目标
- 建立公平、高效的座位预约与使用机制
- 减少占座现象，提高座位利用率
- 避免高并发场景，确保系统稳定运行
- 通过散座机制，合理利用早退释放的座位资源
- 建立违约记录机制，规范学生使用行为

## 2. 系统架构

### 2.1 总体架构
- 前端：微信小程序
- 后端：服务器应用程序
- 硬件设备：门禁闸机、课桌墨水屏（显示二维码）

### 2.2 数据流程
1. 学生通过小程序预约座位
2. 服务器处理预约请求，更新座位状态
3. 学生到达图书馆，通过扫描墨水屏上的二维码签到
4. 系统记录签到、暂离、签退等状态变化
5. 早退情况下，系统自动将座位转为散座状态

## 3. 用户角色

### 3.1 学生用户
- 可预约座位
- 可签到、暂离、签退
- 可查看个人预约记录和违约情况

### 3.2 管理员
- 管理座位资源
- 查看统计数据
- 处理异常情况
- 管理违约记录

## 4. 功能需求

### 4.1 预约功能
1. **分时段预约**
   - 每天分为三个时间段：07:00-12:00、12:00-17:00、17:00-22:00
   - 每位学生每天最多可预约3个时间段
   - 预约开放时间：提前3天开放预约，每天00:00开放次日预约

2. **错峰预约机制**
   - 按学号尾号分批开放预约权限，避免高并发
   - 例如：00:00-00:15开放学号尾号0-2的学生预约，00:15-00:30开放学号尾号3-5的学生预约，以此类推

3. **预约确认流程**
   - 选择日期和时间段
   - 选择教室和座位
   - 确认预约信息
   - 提交预约请求
   - 显示预约成功页面

### 4.2 签到功能
1. **签到规则**
   - 预约时间开始后15分钟内必须完成签到
   - 超时未签到自动取消预约，记录一次违约
   - 签到方式：扫描座位上墨水屏显示的二维码

2. **暂离功能**
   - 使用中可临时暂离，暂离时间不超过30分钟
   - 超过30分钟未返回，系统自动释放座位为散座，记录一次违约
   - 暂离返回需再次扫码确认

3. **签退功能**
   - 主动签退：用户手动操作签退
   - 自动签退：预约时间结束后自动签退
   - 早退情况：预约时间结束前签退，系统自动将座位转为散座

### 4.3 散座功能
1. **散座定义**
   - 因早退或违约而被释放的座位
   - 无需预约，可直接扫码使用

2. **散座使用规则**
   - 扫码即可使用，直到当前时间段结束
   - 使用散座同样需遵守暂离规则
   - 散座使用不计入每日预约次数限制

### 4.4 违约管理
1. **违约情形**
   - 预约后未按时签到
   - 暂离超时未返回
   - 其他违反使用规则的行为

2. **违约处理机制**
   - 一个月内累计3次违约，暂停预约权限7天
   - 一个月内累计5次违约，暂停预约权限14天
   - 一个月内累计7次违约，暂停预约权限30天

### 4.5 用户界面
1. **预约主页**
   - 日期选择
   - 时间段选择
   - 教室列表

2. **座位选择页**
   - 座位布局图
   - 座位状态显示
   - 座位筛选功能

3. **预约确认页**
   - 预约信息确认
   - 预约规则提示

4. **预约成功页**
   - 预约成功提示
   - 预约详情
   - 签到二维码

5. **签到页面**
   - 扫码签到功能
   - 签到状态显示
   - 倒计时提醒

6. **座位使用中页面**
   - 剩余使用时间
   - 暂离功能
   - 签退功能

7. **我的预约列表**
   - 当前预约
   - 历史预约
   - 违约记录

## 5. 非功能需求

### 5.1 性能需求
1. **并发控制**
   - 采用错峰预约机制，避免高并发场景
   - 系统应能支持至少500人同时在线使用

2. **响应时间**
   - 页面加载时间不超过3秒
   - 预约操作响应时间不超过5秒

### 5.2 安全需求
1. **用户认证**
   - 基于微信小程序的用户认证
   - 学生身份验证

2. **数据安全**
   - 用户数据加密存储
   - 定期数据备份

### 5.3 可用性需求
1. **系统可用性**
   - 系统可用率达到99.5%
   - 计划内维护时间提前公告

2. **用户体验**
   - 界面简洁直观
   - 操作流程清晰
   - 提供必要的引导和提示

## 6. 应用流程

### 6.1 预约流程
```
学生打开小程序
  ↓
登录/身份验证
  ↓
选择日期和时间段
  ↓
选择教室
  ↓
查看座位布局，选择座位
  ↓
确认预约信息
  ↓
提交预约请求
  ↓
显示预约成功页面
  ↓
获取签到二维码
```

### 6.2 签到流程
```
学生到达图书馆
  ↓
找到预约的座位
  ↓
打开小程序，进入"我的预约"
  ↓
点击"签到"按钮
  ↓
扫描座位上的二维码
  ↓
签到成功，开始使用座位
```

### 6.3 暂离流程
```
座位使用中
  ↓
点击"暂离"按钮
  ↓
确认暂离
  ↓
系统记录暂离时间，开始30分钟倒计时
  ↓
学生返回座位
  ↓
扫描座位二维码
  ↓
确认返回，继续使用座位
```

### 6.4 签退流程
```
座位使用中
  ↓
点击"签退"按钮
  ↓
确认签退
  ↓
系统记录签退时间
  ↓
如果提前签退，座位转为散座状态
```

### 6.5 散座使用流程
```
学生发现空闲散座
  ↓
扫描座位上的二维码
  ↓
系统显示散座信息
  ↓
确认使用散座
  ↓
开始使用座位，直到当前时间段结束
```

### 6.6 违约处理流程
```
系统检测到违约行为
  ↓
记录违约情况
  ↓
累计违约次数
  ↓
达到处罚阈值
  ↓
系统自动实施相应处罚
  ↓
通知用户
```

## 7. 数据模型

### 7.1 用户信息
- 用户ID
- 学号
- 姓名
- 学院
- 违约次数
- 处罚状态

### 7.2 座位信息
- 座位ID
- 教室ID
- 座位号
- 座位状态（可用/已预约/使用中/暂离中/散座）
- 座位特点（靠窗/有电源等）

### 7.3 预约记录
- 预约ID
- 用户ID
- 座位ID
- 日期
- 时间段
- 预约状态（待签到/使用中/暂离中/已完成/已取消）
- 签到时间
- 签退时间
- 暂离记录

### 7.4 违约记录
- 违约ID
- 用户ID
- 预约ID
- 违约类型
- 违约时间
- 处理状态

## 8. 系统限制与约束

### 8.1 预约限制
- 每位学生每天最多预约3个时间段
- 预约开放时间为提前3天，每天00:00开放次日预约
- 处于违约处罚期的学生无法进行预约

### 8.2 签到限制
- 必须在预约时间开始后15分钟内完成签到
- 只能在预约的座位上签到

### 8.3 暂离限制
- 暂离时间不得超过30分钟
- 每个时间段内最多暂离2次

### 8.4 散座使用限制
- 散座只能使用到当前时间段结束
- 散座同样需遵守暂离规则

## 9. 系统扩展性

### 9.1 未来功能扩展
- 座位预约评分系统
- 座位使用数据分析
- 与图书馆其他系统集成
- 增加团队预约功能（研讨室）

### 9.2 技术扩展
- API接口预留
- 数据库设计考虑扩展性
- 模块化设计便于功能添加

## 10. 实施计划

### 10.1 开发阶段
- 需求分析与设计：2周
- 前端开发：3周
- 后端开发：3周
- 硬件集成：2周
- 测试：2周
- 部署与上线：1周

### 10.2 测试计划
- 单元测试
- 集成测试
- 用户接受测试
- 压力测试

### 10.3 上线策略
- 先在部分教室试点
- 收集反馈并优化
- 逐步推广到所有教室

## 11. 风险评估与应对策略

### 11.1 潜在风险
1. **系统稳定性风险**
   - 风险：系统崩溃或响应缓慢
   - 应对：做好负载测试，实施错峰预约机制

2. **用户接受度风险**
   - 风险：学生不愿使用新系统
   - 应对：加强宣传，提供使用指南，收集反馈持续优化

3. **硬件故障风险**
   - 风险：墨水屏或闸机故障
   - 应对：定期维护，提供故障应急方案

4. **规则漏洞风险**
   - 风险：学生钻系统规则空子
   - 应对：持续监控使用情况，及时调整规则

### 11.2 应急预案
1. **系统故障应急预案**
   - 备用服务器快速切换
   - 临时关闭部分非核心功能

2. **硬件故障应急预案**
   - 提供人工签到渠道
   - 备用设备快速替换

## 12. 总结

本文档详细描述了图书馆座位预约系统的应用流程和需求，重点关注避免高并发场景、处理早退情况下的散座机制以及违约管理。系统通过错峰预约、散座利用和违约处罚机制，旨在提高座位资源利用率，为学生提供公平、高效的座位预约服务。

通过微信小程序与硬件设备的结合，系统实现了从预约、签到到使用的全流程管理，为图书馆座位管理提供了现代化的解决方案。
