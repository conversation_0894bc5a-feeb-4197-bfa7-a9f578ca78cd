<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>管理员登录 - 图书馆座位预约系统</title>
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: "PingFang SC", "Helvetica Neue", Arial, sans-serif;
        }

        body {
            background-color: #f5f5f5;
            color: #333;
            font-size: 14px;
            line-height: 1.5;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
        }

        .login-container {
            width: 100%;
            max-width: 400px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .login-header {
            background: #b1030d;
            color: white;
            padding: 20px;
            text-align: center;
        }

        .login-title {
            font-size: 24px;
            font-weight: 600;
            margin-bottom: 8px;
        }

        .login-subtitle {
            font-size: 14px;
            opacity: 0.8;
        }

        .login-form {
            padding: 30px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-label {
            display: block;
            font-size: 14px;
            font-weight: 500;
            margin-bottom: 8px;
            color: #555;
        }

        .form-input {
            width: 100%;
            height: 44px;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 0 15px;
            font-size: 14px;
            transition: border-color 0.3s;
        }

        .form-input:focus {
            border-color: #b1030d;
            outline: none;
        }

        .login-button {
            width: 100%;
            height: 44px;
            background: #b1030d;
            color: white;
            border: none;
            border-radius: 4px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            transition: background 0.3s;
        }

        .login-button:hover {
            background: #950209;
        }

        .login-footer {
            text-align: center;
            padding: 0 30px 30px;
            color: #777;
            font-size: 13px;
        }

        .login-footer a {
            color: #b1030d;
            text-decoration: none;
        }

        .login-footer a:hover {
            text-decoration: underline;
        }

        .error-message {
            color: #b1030d;
            font-size: 13px;
            margin-top: 5px;
            display: none;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-header">
            <div class="login-title">图书馆座位预约系统</div>
            <div class="login-subtitle">管理员登录</div>
        </div>
        <div class="login-form">
            <div class="form-group">
                <label class="form-label">用户名</label>
                <input type="text" class="form-input" placeholder="请输入管理员用户名">
                <div class="error-message">请输入有效的用户名</div>
            </div>
            <div class="form-group">
                <label class="form-label">密码</label>
                <input type="password" class="form-input" placeholder="请输入密码">
                <div class="error-message">请输入正确的密码</div>
            </div>
            <button class="login-button" onclick="login()">登录</button>
        </div>
        <div class="login-footer">
            <p>© 2023 图书馆座位预约系统 | <a href="#">忘记密码?</a></p>
        </div>
    </div>

    <script>
        function login() {
            // 模拟登录成功
            window.location.href = 'admin_dashboard.html';
        }
    </script>
</body>
</html>
