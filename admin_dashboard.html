<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>仪表盘 - 图书馆座位预约系统</title>
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: "PingFang SC", "Helvetica Neue", Arial, sans-serif;
        }

        body {
            background-color: #f5f5f5;
            color: #333;
            font-size: 14px;
            line-height: 1.5;
            display: flex;
            min-height: 100vh;
        }

        /* 侧边栏样式 */
        .sidebar {
            width: 240px;
            background: #2c3e50;
            color: white;
            height: 100vh;
            position: fixed;
            left: 0;
            top: 0;
            overflow-y: auto;
        }

        .sidebar-header {
            padding: 20px;
            text-align: center;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .sidebar-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 5px;
        }

        .sidebar-subtitle {
            font-size: 12px;
            opacity: 0.7;
        }

        .sidebar-menu {
            padding: 20px 0;
        }

        .menu-item {
            padding: 12px 20px;
            display: flex;
            align-items: center;
            color: rgba(255, 255, 255, 0.7);
            text-decoration: none;
            transition: all 0.3s;
        }

        .menu-item:hover {
            background: rgba(255, 255, 255, 0.1);
            color: white;
        }

        .menu-item.active {
            background: #b1030d;
            color: white;
        }

        .menu-icon {
            margin-right: 10px;
            font-size: 20px;
        }

        .menu-text {
            font-size: 14px;
        }

        /* 主内容区样式 */
        .main-content {
            flex: 1;
            margin-left: 240px;
            padding: 20px;
        }

        /* 顶部导航栏 */
        .top-nav {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 20px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            margin-bottom: 20px;
        }

        .page-title {
            font-size: 20px;
            font-weight: 600;
            color: #333;
        }

        .user-info {
            display: flex;
            align-items: center;
        }

        .user-name {
            margin-right: 10px;
            font-weight: 500;
        }

        .user-avatar {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: #b1030d;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
        }

        /* 统计卡片 */
        .stats-container {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 20px;
            margin-bottom: 20px;
        }

        .stat-card {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
        }

        .stat-icon {
            width: 50px;
            height: 50px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 15px;
            color: white;
            font-size: 24px;
        }

        .stat-icon.blue {
            background: #3498db;
        }

        .stat-icon.green {
            background: #2ecc71;
        }

        .stat-icon.orange {
            background: #f39c12;
        }

        .stat-icon.red {
            background: #e74c3c;
        }

        .stat-value {
            font-size: 24px;
            font-weight: 600;
            margin-bottom: 5px;
        }

        .stat-label {
            font-size: 14px;
            color: #777;
        }

        /* 图表容器 */
        .charts-container {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 20px;
            margin-bottom: 20px;
        }

        .chart-card {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
        }

        .chart-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .chart-title {
            font-size: 16px;
            font-weight: 600;
        }

        .chart-actions {
            display: flex;
        }

        .chart-action {
            padding: 5px 10px;
            background: #f5f5f5;
            border-radius: 4px;
            margin-left: 10px;
            font-size: 12px;
            cursor: pointer;
        }

        .chart-action.active {
            background: #b1030d;
            color: white;
        }

        .chart-content {
            height: 300px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #777;
        }

        /* 最近活动 */
        .recent-activities {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
        }

        .activities-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .activities-title {
            font-size: 16px;
            font-weight: 600;
        }

        .view-all {
            color: #b1030d;
            font-size: 14px;
            cursor: pointer;
        }

        .activity-item {
            display: flex;
            padding: 15px 0;
            border-bottom: 1px solid #f0f0f0;
        }

        .activity-icon {
            width: 40px;
            height: 40px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
            color: white;
            font-size: 20px;
        }

        .activity-icon.blue {
            background: #3498db;
        }

        .activity-icon.green {
            background: #2ecc71;
        }

        .activity-icon.orange {
            background: #f39c12;
        }

        .activity-icon.red {
            background: #e74c3c;
        }

        .activity-content {
            flex: 1;
        }

        .activity-title {
            font-size: 14px;
            font-weight: 500;
            margin-bottom: 5px;
        }

        .activity-time {
            font-size: 12px;
            color: #777;
        }

        /* 响应式设计 */
        @media (max-width: 1200px) {
            .stats-container {
                grid-template-columns: repeat(2, 1fr);
            }
            
            .charts-container {
                grid-template-columns: 1fr;
            }
        }

        @media (max-width: 768px) {
            .sidebar {
                width: 70px;
            }
            
            .sidebar-title, .sidebar-subtitle, .menu-text {
                display: none;
            }
            
            .menu-icon {
                margin-right: 0;
            }
            
            .main-content {
                margin-left: 70px;
            }
        }
    </style>
</head>
<body>
    <!-- 侧边栏 -->
    <div class="sidebar">
        <div class="sidebar-header">
            <div class="sidebar-title">图书馆座位预约系统</div>
            <div class="sidebar-subtitle">管理后台</div>
        </div>
        <div class="sidebar-menu">
            <a href="admin_dashboard.html" class="menu-item active">
                <span class="material-icons menu-icon">dashboard</span>
                <span class="menu-text">仪表盘</span>
            </a>
            <a href="admin_seats.html" class="menu-item">
                <span class="material-icons menu-icon">event_seat</span>
                <span class="menu-text">座位管理</span>
            </a>
            <a href="admin_rooms.html" class="menu-item">
                <span class="material-icons menu-icon">meeting_room</span>
                <span class="menu-text">教室管理</span>
            </a>
            <a href="admin_bookings.html" class="menu-item">
                <span class="material-icons menu-icon">book_online</span>
                <span class="menu-text">预约记录</span>
            </a>
            <a href="admin_violations.html" class="menu-item">
                <span class="material-icons menu-icon">gavel</span>
                <span class="menu-text">违约记录</span>
            </a>
            <a href="admin_users.html" class="menu-item">
                <span class="material-icons menu-icon">people</span>
                <span class="menu-text">用户管理</span>
            </a>
            <a href="admin_settings.html" class="menu-item">
                <span class="material-icons menu-icon">settings</span>
                <span class="menu-text">系统设置</span>
            </a>
            <a href="admin_login.html" class="menu-item">
                <span class="material-icons menu-icon">logout</span>
                <span class="menu-text">退出登录</span>
            </a>
        </div>
    </div>

    <!-- 主内容区 -->
    <div class="main-content">
        <!-- 顶部导航栏 -->
        <div class="top-nav">
            <div class="page-title">仪表盘</div>
            <div class="user-info">
                <span class="user-name">管理员</span>
                <div class="user-avatar">A</div>
            </div>
        </div>

        <!-- 统计卡片 -->
        <div class="stats-container">
            <div class="stat-card">
                <div class="stat-icon blue">
                    <span class="material-icons">event_seat</span>
                </div>
                <div class="stat-value">1,245</div>
                <div class="stat-label">今日预约数</div>
            </div>
            <div class="stat-card">
                <div class="stat-icon green">
                    <span class="material-icons">check_circle</span>
                </div>
                <div class="stat-value">876</div>
                <div class="stat-label">今日签到数</div>
            </div>
            <div class="stat-card">
                <div class="stat-icon orange">
                    <span class="material-icons">timer</span>
                </div>
                <div class="stat-value">124</div>
                <div class="stat-label">今日暂离数</div>
            </div>
            <div class="stat-card">
                <div class="stat-icon red">
                    <span class="material-icons">gavel</span>
                </div>
                <div class="stat-value">32</div>
                <div class="stat-label">今日违约数</div>
            </div>
        </div>

        <!-- 图表区域 -->
        <div class="charts-container">
            <div class="chart-card">
                <div class="chart-header">
                    <div class="chart-title">预约与签到统计</div>
                    <div class="chart-actions">
                        <div class="chart-action">日</div>
                        <div class="chart-action active">周</div>
                        <div class="chart-action">月</div>
                    </div>
                </div>
                <div class="chart-content">
                    [此处将显示预约与签到统计图表]
                </div>
            </div>
            <div class="chart-card">
                <div class="chart-header">
                    <div class="chart-title">座位使用率</div>
                    <div class="chart-actions">
                        <div class="chart-action active">今日</div>
                        <div class="chart-action">本周</div>
                    </div>
                </div>
                <div class="chart-content">
                    [此处将显示座位使用率饼图]
                </div>
            </div>
        </div>

        <!-- 最近活动 -->
        <div class="recent-activities">
            <div class="activities-header">
                <div class="activities-title">最近活动</div>
                <div class="view-all">查看全部</div>
            </div>
            <div class="activity-item">
                <div class="activity-icon green">
                    <span class="material-icons">check_circle</span>
                </div>
                <div class="activity-content">
                    <div class="activity-title">学生 张三 (2020001) 在 A101 教室签到</div>
                    <div class="activity-time">10分钟前</div>
                </div>
            </div>
            <div class="activity-item">
                <div class="activity-icon blue">
                    <span class="material-icons">event_seat</span>
                </div>
                <div class="activity-content">
                    <div class="activity-title">学生 李四 (2020002) 预约了 B201 教室的座位</div>
                    <div class="activity-time">25分钟前</div>
                </div>
            </div>
            <div class="activity-item">
                <div class="activity-icon orange">
                    <span class="material-icons">timer</span>
                </div>
                <div class="activity-content">
                    <div class="activity-title">学生 王五 (2020003) 在 C301 教室暂离</div>
                    <div class="activity-time">40分钟前</div>
                </div>
            </div>
            <div class="activity-item">
                <div class="activity-icon red">
                    <span class="material-icons">gavel</span>
                </div>
                <div class="activity-content">
                    <div class="activity-title">学生 赵六 (2020004) 在 A102 教室暂离超时</div>
                    <div class="activity-time">1小时前</div>
                </div>
            </div>
            <div class="activity-item">
                <div class="activity-icon green">
                    <span class="material-icons">logout</span>
                </div>
                <div class="activity-content">
                    <div class="activity-title">学生 孙七 (2020005) 在 B202 教室签退</div>
                    <div class="activity-time">2小时前</div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
