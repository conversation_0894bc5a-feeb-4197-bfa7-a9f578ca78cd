<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>确认预约 - 图书馆座位预约</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            -webkit-tap-highlight-color: transparent;
            font-family: "PingFang SC", "Helvetica Neue", Arial, sans-serif;
        }

        body {
            background-color: #f8f8f8;
            color: #333;
            font-size: 14px;
            line-height: 1.5;
            overflow-x: hidden;
        }

        /* 模拟iPhone 15 Pro Max */
        .device-container {
            width: 100%;
            max-width: 430px;
            height: 932px;
            margin: 20px auto;
            position: relative;
            overflow: hidden;
            border-radius: 55px;
            box-shadow: 0 20px 50px rgba(0, 0, 0, 0.3);
            background: #1a1a1a;
            padding: 12px;
        }

        /* 刘海区域 */
        .notch {
            position: absolute;
            width: 160px;
            height: 34px;
            background: #1a1a1a;
            top: 12px;
            left: 50%;
            transform: translateX(-50%);
            border-radius: 0 0 20px 20px;
            z-index: 100;
        }

        /* 底部指示条 */
        .home-indicator {
            position: absolute;
            width: 140px;
            height: 5px;
            background: #ffffff;
            bottom: 25px;
            left: 50%;
            transform: translateX(-50%);
            border-radius: 3px;
            z-index: 100;
        }

        /* 手机屏幕 */
        .screen {
            width: 100%;
            height: 100%;
            background: #ffffff;
            border-radius: 45px;
            overflow: hidden;
            position: relative;
        }

        /* 小程序内容区域 */
        .app-container {
            width: 100%;
            height: 100%;
            overflow: hidden;
            display: flex;
            flex-direction: column;
        }

        /* 顶部导航栏 */
        .nav-bar {
            height: 88px;
            background-color: #b1030d;
            color: white;
            display: flex;
            justify-content: space-between;
            align-items: flex-end;
            padding: 0 16px 10px;
            position: relative;
            z-index: 10;
        }

        .nav-left {
            font-size: 24px;
            padding-bottom: 10px;
        }

        .nav-title {
            font-size: 18px;
            font-weight: 600;
            padding-bottom: 10px;
        }

        .nav-right {
            width: 24px;
            padding-bottom: 10px;
        }

        /* 主内容区 */
        .main-content {
            flex: 1;
            overflow-y: auto;
            -webkit-overflow-scrolling: touch;
            padding: 16px;
            padding-bottom: 90px;
        }

        /* 预约信息卡片 */
        .booking-card {
            background: white;
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            animation: fadeIn 0.5s ease forwards;
        }

        .booking-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
            padding-bottom: 16px;
            border-bottom: 1px solid #f0f0f0;
        }

        .booking-title {
            font-size: 18px;
            font-weight: 600;
            color: #333;
        }

        .booking-info-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 16px;
        }

        .booking-info-item {
            display: flex;
            flex-direction: column;
        }

        .info-label {
            font-size: 13px;
            color: #999;
            margin-bottom: 4px;
        }

        .info-value {
            font-size: 16px;
            color: #333;
            font-weight: 500;
        }

        .info-value.highlight {
            color: #b1030d;
            font-weight: 600;
        }

        /* 座位预览 */
        .seat-preview {
            background: white;
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            animation: fadeIn 0.5s ease forwards;
            animation-delay: 0.1s;
            opacity: 0;
        }

        .preview-title {
            font-size: 16px;
            font-weight: 600;
            color: #333;
            margin-bottom: 16px;
        }

        .seat-map-container {
            width: 100%;
            height: 200px;
            background: #f9f9f9;
            border-radius: 8px;
            position: relative;
            overflow: hidden;
        }

        .seat-map {
            width: 100%;
            height: 100%;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
        }

        .entrance {
            width: 100px;
            height: 24px;
            background: #eacdcf;
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            color: #b1030d;
            margin-bottom: 16px;
        }

        .seat-grid {
            display: grid;
            grid-template-columns: repeat(8, 1fr);
            gap: 6px;
            width: 100%;
            max-width: 320px;
        }

        .seat-item {
            width: 24px;
            height: 24px;
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 10px;
            color: white;
        }

        .seat-available {
            background-color: #4CAF50;
        }

        .seat-occupied {
            background-color: #9e9e9e;
        }

        .seat-selected {
            background-color: #b1030d;
            transform: scale(1.1);
            box-shadow: 0 2px 8px rgba(177, 3, 13, 0.3);
        }

        .seat-empty {
            visibility: hidden;
        }

        .seat-features {
            display: flex;
            flex-wrap: wrap;
            margin-top: 16px;
        }

        .seat-feature {
            display: flex;
            align-items: center;
            margin-right: 16px;
            margin-bottom: 8px;
            font-size: 13px;
            color: #666;
        }

        .seat-feature i {
            margin-right: 4px;
            color: #b1030d;
            font-size: 16px;
        }

        /* 预约须知 */
        .booking-rules {
            background: white;
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            animation: fadeIn 0.5s ease forwards;
            animation-delay: 0.2s;
            opacity: 0;
        }

        .rules-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }

        .rules-title {
            font-size: 16px;
            font-weight: 600;
            color: #333;
        }

        .rules-toggle {
            color: #b1030d;
            font-size: 13px;
            display: flex;
            align-items: center;
        }

        .rules-toggle i {
            margin-left: 4px;
            transition: transform 0.3s ease;
        }

        .rules-toggle.expanded i {
            transform: rotate(180deg);
        }

        .rules-content {
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease;
        }

        .rules-content.expanded {
            max-height: 500px;
        }

        .rule-item {
            display: flex;
            margin-bottom: 12px;
        }

        .rule-number {
            width: 20px;
            height: 20px;
            border-radius: 10px;
            background: #f5f5f5;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            color: #666;
            margin-right: 8px;
            flex-shrink: 0;
        }

        .rule-text {
            font-size: 13px;
            color: #666;
            flex: 1;
        }

        /* 底部操作栏 */
        .bottom-bar {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            padding: 16px;
            background: white;
            box-shadow: 0 -1px 10px rgba(0, 0, 0, 0.05);
            z-index: 100;
            display: flex;
            justify-content: space-between;
            animation: slideUp 0.5s ease forwards;
            animation-delay: 0.3s;
            opacity: 0;
            transform: translateY(20px);
        }

        .cancel-btn {
            width: 30%;
            height: 50px;
            border-radius: 25px;
            background: #f5f5f5;
            color: #666;
            font-size: 16px;
            font-weight: 600;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .confirm-btn {
            width: 65%;
            height: 50px;
            border-radius: 25px;
            background: #b1030d;
            color: white;
            font-size: 16px;
            font-weight: 600;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        /* 底部导航 */
        .tab-bar {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: white;
            display: flex;
            box-shadow: 0 -1px 10px rgba(0, 0, 0, 0.05);
            z-index: 90;
        }

        .tab-item {
            flex: 1;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            color: #999;
            font-size: 10px;
            transition: all 0.3s ease;
        }

        .tab-item.active {
            color: #b1030d;
        }

        .tab-icon {
            font-size: 24px;
            margin-bottom: 2px;
        }

        /* 图标字体 */
        .icon {
            font-family: "Material Icons";
            font-weight: normal;
            font-style: normal;
            font-size: 24px;
            display: inline-block;
            line-height: 1;
            text-transform: none;
            letter-spacing: normal;
            word-wrap: normal;
            white-space: nowrap;
            direction: ltr;
            -webkit-font-smoothing: antialiased;
            text-rendering: optimizeLegibility;
            -moz-osx-font-smoothing: grayscale;
            font-feature-settings: 'liga';
        }

        /* 动画效果 */
        @keyframes fadeIn {
            from {
                opacity: 0;
            }
            to {
                opacity: 1;
            }
        }

        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* 加载动画 */
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(255, 255, 255, 0.8);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1000;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
        }

        .loading-overlay.show {
            opacity: 1;
            visibility: visible;
        }

        .loading-spinner {
            width: 40px;
            height: 40px;
            border: 4px solid #f3f3f3;
            border-top: 4px solid #b1030d;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
</head>
<body>
    <div class="device-container">
        <div class="notch"></div>
        <div class="home-indicator"></div>
        <div class="screen">
            <div class="app-container">
                <!-- 顶部导航栏 -->
                <div class="nav-bar">
                    <div class="nav-left">
                        <i class="icon" onclick="goBack()">arrow_back</i>
                    </div>
                    <div class="nav-title">确认预约</div>
                    <div class="nav-right"></div>
                </div>

                <!-- 主内容区 -->
                <div class="main-content">
                    <!-- 预约信息卡片 -->
                    <div class="booking-card">
                        <div class="booking-header">
                            <div class="booking-title">A101 自习室</div>
                        </div>
                        <div class="booking-info-grid">
                            <div class="booking-info-item">
                                <div class="info-label">座位号</div>
                                <div class="info-value highlight">A5</div>
                            </div>
                            <div class="booking-info-item">
                                <div class="info-label">日期</div>
                                <div class="info-value">2023年5月17日</div>
                            </div>
                            <div class="booking-info-item">
                                <div class="info-label">时间段</div>
                                <div class="info-value">12:00 - 17:00</div>
                            </div>
                            <div class="booking-info-item">
                                <div class="info-label">位置</div>
                                <div class="info-value">图书馆1楼</div>
                            </div>
                        </div>
                    </div>

                    <!-- 座位预览 -->
                    <!-- <div class="seat-preview">
                        <div class="preview-title">座位预览</div>
                        <div class="seat-map-container">
                            <div class="seat-map">
                                <div class="entrance">
                                    <i class="icon" style="font-size: 16px;">door_front</i> 入口
                                </div>
                                <div class="seat-grid" id="seatGrid">
                                     
                                </div>
                            </div>
                        </div>
                        <div class="seat-features">
                            <div class="seat-feature">
                                <i class="icon">event_seat</i> 座位号: A5
                            </div>
                            <div class="seat-feature">
                                <i class="icon">window</i> 靠窗
                            </div>
                            <div class="seat-feature">
                                <i class="icon">power</i> 有电源
                            </div>
                            <div class="seat-feature">
                                <i class="icon">visibility</i> 视野好
                            </div>
                        </div>
                    </div> -->

                    <!-- 预约须知 -->
                    <div class="booking-rules">
                        <div class="rules-header">
                            <div class="rules-title">预约须知   （默认展开 不需要弹出和隐藏）</div>
                            <div class="rules-toggle " id="rulesToggle" onclick="toggleRules()">
                                查看详情 <i class="icon" style="font-size: 18px;">keyboard_arrow_down</i>
                            </div>
                        </div>
                        <div class="rules-content" id="rulesContent">
                            <div class="rule-item">
                                <div class="rule-number">1</div>
                                <div class="rule-text">预约成功后，请在预约时间开始后15分钟内完成签到，否则系统将自动取消预约。</div>
                            </div>
                            <div class="rule-item">
                                <div class="rule-number">2</div>
                                <div class="rule-text">使用过程中可临时离开，但暂离时间不得超过30分钟，否则座位将被释放。</div>
                            </div>
                            <div class="rule-item">
                                <div class="rule-number">3</div>
                                <div class="rule-text">每人每天最多可预约3个时间段，每个时间段为5小时。</div>
                            </div>
                            <div class="rule-item">
                                <div class="rule-number">4</div>
                                <div class="rule-text">如需取消预约，请提前30分钟操作，否则将计入违约记录。</div>
                            </div>
                            <div class="rule-item">
                                <div class="rule-number">5</div>
                                <div class="rule-text">一个月内累计3次违约，将暂停预约权限7天。</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 底部操作栏 -->
                <div class="bottom-bar">
                    <div class="cancel-btn" onclick="cancelBooking()">取消</div>
                    <div class="confirm-btn" onclick="confirmBooking()">确认预约</div>
                </div>

                <!-- 底部导航栏 -->
                <div class="tab-bar">
                    <div class="tab-item active">
                        <i class="icon tab-icon">event_seat</i>
                        <div>预约</div>
                    </div>
                    <div class="tab-item">
                        <i class="icon tab-icon">schedule</i>
                        <div>我的座位</div>
                    </div>
                    <div class="tab-item">
                        <i class="icon tab-icon">person</i>
                        <div>我的</div>
                    </div>
                </div>

                <!-- 加载动画 -->
                <div class="loading-overlay" id="loadingOverlay">
                    <div class="loading-spinner"></div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 座位数据（模拟）
        const seatData = [
            // 0表示无座位，1表示可用，2表示已占用，3表示选中
            [0, 1, 1, 0, 0, 1, 1, 0],
            [1, 1, 2, 2, 1, 1, 2, 1],
            [1, 2, 3, 1, 2, 1, 1, 1], // A5是选中的座位
            [2, 1, 1, 2, 1, 2, 1, 2],
            [1, 1, 2, 1, 1, 1, 2, 1],
            [2, 1, 1, 2, 1, 2, 1, 1]
        ];

        // 初始化座位布局
        function initSeatLayout() {
            const seatGrid = document.getElementById('seatGrid');
            seatGrid.innerHTML = '';

            const rows = ['A', 'B', 'C', 'D', 'E', 'F'];
            
            for (let i = 0; i < seatData.length; i++) {
                for (let j = 0; j < seatData[i].length; j++) {
                    const seatValue = seatData[i][j];
                    const seatId = `${rows[i]}${j+1}`;
                    
                    const seat = document.createElement('div');
                    seat.className = 'seat-item';
                    
                    if (seatValue === 0) {
                        seat.classList.add('seat-empty');
                    } else if (seatValue === 1) {
                        seat.classList.add('seat-available');
                    } else if (seatValue === 2) {
                        seat.classList.add('seat-occupied');
                    } else if (seatValue === 3) {
                        seat.classList.add('seat-selected');
                    }
                    
                    seat.textContent = seatId;
                    seatGrid.appendChild(seat);
                }
            }
        }

        // 切换预约须知展开/收起
        function toggleRules() {
            const rulesToggle = document.getElementById('rulesToggle');
            const rulesContent = document.getElementById('rulesContent');
            
            rulesToggle.classList.toggle('expanded');
            rulesContent.classList.toggle('expanded');
            
            if (rulesToggle.classList.contains('expanded')) {
                rulesToggle.innerHTML = '收起 <i class="icon" style="font-size: 18px;">keyboard_arrow_up</i>';
            } else {
                rulesToggle.innerHTML = '查看详情 <i class="icon" style="font-size: 18px;">keyboard_arrow_down</i>';
            }
        }

        // 取消预约
        function cancelBooking() {
            // 返回上一页
            goBack();
        }

        // 确认预约
        function confirmBooking() {
            // 显示加载动画
            document.getElementById('loadingOverlay').classList.add('show');
            
            // 模拟网络请求延迟
            setTimeout(() => {
                // 隐藏加载动画
                document.getElementById('loadingOverlay').classList.remove('show');
                
                // 跳转到预约成功页面
                window.location.href = 'booking_success.html';
            }, 1500);
        }

        // 返回上一页
        function goBack() {
            // 实际应用中应该使用微信小程序的导航API
            alert('返回上一页');
            // wx.navigateBack();
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            initSeatLayout();
        });
    </script>
</body>
</html>
