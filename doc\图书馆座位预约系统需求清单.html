<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>图书馆座位预约系统需求清单</title>
    <style>
        body {
            font-family: "Microsoft YaHei", Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
            line-height: 1.6;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        h1 {
            text-align: center;
            color: #b1030d;
            margin-bottom: 30px;
            font-size: 28px;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 30px;
            font-size: 14px;
        }
        
        th, td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
            vertical-align: top;
        }
        
        th {
            background-color: #b1030d;
            color: white;
            font-weight: bold;
            text-align: center;
            font-size: 16px;
        }
        
        .platform-header {
            background-color: #f8f9fa;
            font-weight: bold;
            color: #333;
            font-size: 16px;
            text-align: center;
        }
        
        .module-name {
            font-weight: bold;
            color: #b1030d;
            width: 120px;
            text-align: center;
        }
        
        .function-name {
            font-weight: bold;
            color: #333;
            width: 150px;
        }
        
        .description {
            color: #555;
            line-height: 1.8;
        }
        
        .description ul {
            margin: 8px 0;
            padding-left: 20px;
        }
        
        .description li {
            margin: 4px 0;
        }
        
        .highlight {
            color: #b1030d;
            font-weight: bold;
        }
        
        .note {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 4px;
            padding: 8px;
            margin: 8px 0;
            font-size: 13px;
            color: #856404;
        }
        
        .feature-tag {
            display: inline-block;
            background-color: #e3f2fd;
            color: #1976d2;
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 12px;
            margin: 2px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>图书馆座位预约系统需求清单</h1>
        
        <table>
            <thead>
                <tr>
                    <th style="width: 8%;">平台</th>
                    <th style="width: 12%;">功能模块</th>
                    <th style="width: 15%;">功能名称</th>
                    <th style="width: 65%;">功能描述</th>
                </tr>
            </thead>
            <tbody>
                <!-- 管理端 -->
                <tr class="platform-header">
                    <td rowspan="15" class="platform-header">管理端</td>
                    <td class="module-name">管理员认证</td>
                    <td class="function-name">登录管理</td>
                    <td class="description">
                        管理员通过账号密码登录后台系统，支持登录状态保持、密码修改、密码找回等功能；
                        <span class="feature-tag">安全验证</span>
                        <span class="feature-tag">权限控制</span>
                        <div class="note">注：支持多级管理员权限，包括超级管理员、普通管理员等角色</div>
                    </td>
                </tr>
                
                <tr>
                    <td class="module-name">数据概览</td>
                    <td class="function-name">仪表盘</td>
                    <td class="description">
                        展示系统核心数据统计，包括：
                        <ul>
                            <li><span class="highlight">今日预约数</span>：当日总预约量统计</li>
                            <li><span class="highlight">今日签到数</span>：当日实际签到量统计</li>
                            <li><span class="highlight">今日暂离数</span>：当日暂离操作统计</li>
                            <li><span class="highlight">今日违约数</span>：当日违约行为统计</li>
                            <li><span class="highlight">预约与签到统计图表</span>：可按日/周/月查看趋势</li>
                            <li><span class="highlight">座位使用率图表</span>：实时座位利用率分析</li>
                            <li><span class="highlight">最近活动记录</span>：系统最新操作日志</li>
                        </ul>
                        <span class="feature-tag">数据可视化</span>
                        <span class="feature-tag">实时统计</span>
                    </td>
                </tr>
                
                <tr>
                    <td class="module-name">教室管理</td>
                    <td class="function-name">教室信息管理</td>
                    <td class="description">
                        管理、维护教室基础信息，包括教室名称、位置、容量、开放时间、设施配置等；
                        教室信息将作为底层数据对接座位管理和预约功能模块；
                        支持教室状态管理（正常/维护/关闭），可设置教室开放时间段；
                        <span class="feature-tag">基础数据</span>
                        <span class="feature-tag">状态管理</span>
                        <div class="note">注：教室数据变更会影响相关座位的可预约状态</div>
                    </td>
                </tr>
                
                <tr>
                    <td class="module-name">座位管理</td>
                    <td class="function-name">座位信息管理</td>
                    <td class="description">
                        座位信息管理，包括座位编号、所属教室、座位特点（靠窗、有电源、视野好等）、座位状态、位置坐标、备注信息等；
                        支持座位布局可视化管理，可通过拖拽方式调整座位位置；
                        可批量导入/导出座位信息，支持座位状态批量操作；
                        可关联自定义表单来完善座位属性字段内容；
                        <span class="feature-tag">可视化管理</span>
                        <span class="feature-tag">批量操作</span>
                        <span class="feature-tag">状态监控</span>
                    </td>
                </tr>
                
                <tr>
                    <td class="module-name">用户管理</td>
                    <td class="function-name">学生账号管理</td>
                    <td class="description">
                        管理维护学生账号资料信息，包括学号、姓名、学院、专业、年级、联系方式、身份验证状态等；
                        支持学生账号状态管理（正常/受限/禁用），可设置预约权限；
                        可查看学生预约历史、违约记录、使用统计等信息；
                        支持批量导入学生信息，可导出用户数据报表；
                        <span class="feature-tag">身份管理</span>
                        <span class="feature-tag">权限控制</span>
                        <span class="feature-tag">数据统计</span>
                    </td>
                </tr>
                
                <tr>
                    <td class="module-name">预约管理</td>
                    <td class="function-name">预约记录管理</td>
                    <td class="description">
                        通过学号、教室、座位号、预约时间范围、预约状态查询预约记录，可导出；
                        预约记录包括预约ID、学生信息、座位信息、预约时间、状态变更记录等；
                        支持预约详情查看，包含完整的状态变更时间线；
                        可进行预约管理操作：强制取消预约、强制签退、状态修正等；
                        <span class="feature-tag">记录查询</span>
                        <span class="feature-tag">状态管理</span>
                        <span class="feature-tag">数据导出</span>
                    </td>
                </tr>
                
                <tr>
                    <td class="module-name">违约管理</td>
                    <td class="function-name">违约记录管理</td>
                    <td class="description">
                        通过学号、违约类型、违约时间范围、处理状态查询违约记录，可导出；
                        违约记录包括违约类型（未签到、暂离超时）、违约时间、相关预约信息、处理状态等；
                        支持违约记录撤销、用户处罚状态管理（警告、暂停预约权限）；
                        可查看违约统计分析，包括违约趋势、违约类型分布等；
                        <span class="feature-tag">违约统计</span>
                        <span class="feature-tag">处罚管理</span>
                        <span class="feature-tag">记录撤销</span>
                    </td>
                </tr>
                
                <tr>
                    <td class="module-name">表单管理</td>
                    <td class="function-name">自定义表单</td>
                    <td class="description">
                        可通过单行文本、多行文本、单选、多选、日期选择、数字输入、图片上传等组件自由创建表单；
                        表单将作为底层数据对接座位属性、用户信息扩展等功能应用模块；
                        支持表单模板管理、表单数据统计分析；
                        <span class="feature-tag">组件化</span>
                        <span class="feature-tag">数据扩展</span>
                    </td>
                </tr>
                
                <tr>
                    <td class="module-name">系统设置</td>
                    <td class="function-name">预约规则配置</td>
                    <td class="description">
                        配置预约相关规则参数：
                        <ul>
                            <li>预约开放时间（提前几天开放预约）</li>
                            <li>时间段设置（上午、下午、晚上时间段划分）</li>
                            <li>预约限制（每人每天最多预约次数）</li>
                            <li>签到时限（预约后多长时间内必须签到）</li>
                            <li>暂离时限（最长暂离时间、每时段暂离次数限制）</li>
                            <li>违约处罚规则（违约次数与处罚力度对应关系）</li>
                        </ul>
                        <span class="feature-tag">规则配置</span>
                        <span class="feature-tag">参数管理</span>
                    </td>
                </tr>
                
                <tr>
                    <td class="module-name">操作日志</td>
                    <td class="function-name">系统日志管理</td>
                    <td class="description">
                        登记所有管理员和用户的操作记录，包括登录日志、操作日志、异常日志等；
                        支持按操作人、操作类型、时间范围查询日志记录；
                        记录详细的操作内容、IP地址、操作时间等信息；
                        <span class="feature-tag">操作追踪</span>
                        <span class="feature-tag">安全审计</span>
                    </td>
                </tr>
                
                <tr>
                    <td class="module-name">统计报表</td>
                    <td class="function-name">数据分析报表</td>
                    <td class="description">
                        生成各类统计报表：
                        <ul>
                            <li><span class="highlight">座位使用率报表</span>：按教室、时间段统计座位利用率</li>
                            <li><span class="highlight">预约统计报表</span>：预约量趋势、预约成功率分析</li>
                            <li><span class="highlight">违约统计报表</span>：违约行为分析、用户违约排行</li>
                            <li><span class="highlight">用户活跃度报表</span>：用户使用频率、活跃时段分析</li>
                        </ul>
                        支持报表导出（Excel、PDF格式），可设置定期自动生成报表；
                        <span class="feature-tag">数据分析</span>
                        <span class="feature-tag">报表导出</span>
                    </td>
                </tr>
                
                <!-- 微信小程序端 -->
                <tr class="platform-header">
                    <td rowspan="12" class="platform-header">微信小程序端</td>
                    <td class="module-name">用户认证</td>
                    <td class="function-name">登录授权</td>
                    <td class="description">
                        授权用户可通过微信授权直接登录小程序，自动获取微信基本信息；
                        支持学生身份验证，绑定学号、姓名、学院等学籍信息；
                        登录后自动检查用户权限状态（是否被暂停预约）；
                        <span class="feature-tag">微信授权</span>
                        <span class="feature-tag">身份验证</span>
                    </td>
                </tr>
                
                <tr>
                    <td class="module-name">个人中心</td>
                    <td class="function-name">个人信息管理</td>
                    <td class="description">
                        可查看个人基本信息资料、学籍信息、预约统计数据；
                        可查看个人违约记录、当前处罚状态；
                        支持个人偏好设置、消息通知设置；
                        <span class="feature-tag">信息展示</span>
                        <span class="feature-tag">偏好设置</span>
                    </td>
                </tr>
                
                <tr>
                    <td class="module-name">座位预约</td>
                    <td class="function-name">预约主页</td>
                    <td class="description">
                        可按日期选择（提前3天开放预约）、时间段选择（上午/下午/晚上）进行预约；
                        可按教室筛选，显示各教室可用座位数量；
                        实施错峰预约机制，按学号尾号分批开放预约权限，避免高并发；
                        显示用户当日已预约次数和剩余可预约次数；
                        <span class="feature-tag">错峰预约</span>
                        <span class="feature-tag">时段管理</span>
                    </td>
                </tr>
                
                <tr>
                    <td class="module-name">座位预约</td>
                    <td class="function-name">座位选择</td>
                    <td class="description">
                        可按座位特点筛选（全部、有电源、靠窗、安静区等）、可按座位状态筛选；
                        座位布局图可视化展示，支持缩放操作，座位状态实时更新；
                        座位详情展示包括座位号、特点标识、当前状态等信息；
                        选择座位后可查看该座位的使用历史和评价信息；
                        <span class="feature-tag">可视化选择</span>
                        <span class="feature-tag">实时状态</span>
                        <span class="feature-tag">筛选功能</span>
                    </td>
                </tr>
                
                <tr>
                    <td class="module-name">座位预约</td>
                    <td class="function-name">预约确认</td>
                    <td class="description">
                        确认预约信息展示，包括教室、座位、日期、时间段等详细信息；
                        显示座位预览图和座位特点说明；
                        展示预约须知和使用规则，用户需确认同意后方可提交；
                        提交预约后显示预约成功页面，生成签到二维码；
                        <span class="feature-tag">信息确认</span>
                        <span class="feature-tag">规则提示</span>
                    </td>
                </tr>
                
                <tr>
                    <td class="module-name">签到使用</td>
                    <td class="function-name">扫码签到</td>
                    <td class="description">
                        用户到达图书馆后，可扫描座位上的二维码进行签到；
                        签到页面显示预约信息、签到倒计时（15分钟内必须签到）；
                        签到成功后进入座位使用状态，开始计时；
                        支持签到异常处理（座位故障、二维码损坏等情况）；
                        <span class="feature-tag">二维码签到</span>
                        <span class="feature-tag">时限控制</span>
                    </td>
                </tr>
                
                <tr>
                    <td class="module-name">签到使用</td>
                    <td class="function-name">座位使用管理</td>
                    <td class="description">
                        座位使用中页面显示剩余使用时间（环形进度条展示）；
                        显示使用统计信息：已使用时长、暂离次数等；
                        支持暂离功能：可临时离开最长30分钟，每时段最多暂离2次；
                        支持签退功能：主动结束使用，早退座位自动转为散座状态；
                        <span class="feature-tag">时间管理</span>
                        <span class="feature-tag">暂离控制</span>
                        <span class="feature-tag">状态切换</span>
                    </td>
                </tr>
                
                <tr>
                    <td class="module-name">预约管理</td>
                    <td class="function-name">我的预约</td>
                    <td class="description">
                        可查看当前预约和历史预约记录，按状态分类展示；
                        预约状态包括：待签到、使用中、暂离中、已完成、已取消、违约；
                        支持预约操作：签到、取消预约、查看详情、再次预约等；
                        可查看预约统计数据：总预约次数、成功率、平均使用时长等；
                        <span class="feature-tag">记录管理</span>
                        <span class="feature-tag">状态跟踪</span>
                    </td>
                </tr>
                
                <tr>
                    <td class="module-name">违约管理</td>
                    <td class="function-name">违约记录查看</td>
                    <td class="description">
                        可查看个人违约记录列表，包括违约类型、时间、相关预约信息；
                        显示当前违约状态：正常/警告/受限，以及本月违约次数统计；
                        展示违约规则说明，帮助用户了解违约后果和避免违约；
                        支持违约申诉功能，可提交特殊情况说明；
                        <span class="feature-tag">记录查看</span>
                        <span class="feature-tag">规则说明</span>
                    </td>
                </tr>
                
                <tr>
                    <td class="module-name">散座功能</td>
                    <td class="function-name">散座使用</td>
                    <td class="description">
                        可发现和使用散座（因早退或违约释放的座位）；
                        扫描座位二维码后可查看座位状态，如为散座可直接使用；
                        散座使用无需预约，可使用到当前时间段结束；
                        散座使用同样需遵守暂离规则，使用不计入每日预约次数限制；
                        <span class="feature-tag">即时使用</span>
                        <span class="feature-tag">状态识别</span>
                    </td>
                </tr>
                
                <tr>
                    <td class="module-name">座位查询</td>
                    <td class="function-name">扫码查询座位</td>
                    <td class="description">
                        扫描任意座位二维码可查看该座位的详细信息和当前状态；
                        可查询该座位在指定日期、时间段的可用情况；
                        支持座位状态查看：空闲、使用中、已预约、暂离中、故障等；
                        可直接对空闲座位进行预约操作，或报告座位故障；
                        <span class="feature-tag">状态查询</span>
                        <span class="feature-tag">即时预约</span>
                        <span class="feature-tag">故障报告</span>
                    </td>
                </tr>
                
                <tr>
                    <td class="module-name">消息通知</td>
                    <td class="function-name">消息推送</td>
                    <td class="description">
                        支持预约提醒、签到提醒、暂离超时提醒等消息推送；
                        违约通知、处罚通知等重要消息及时推送；
                        系统公告、维护通知等信息发布；
                        用户可设置消息接收偏好和推送时间；
                        <span class="feature-tag">消息推送</span>
                        <span class="feature-tag">提醒服务</span>
                    </td>
                </tr>
            </tbody>
        </table>
        
        <div style="margin-top: 30px; padding: 20px; background-color: #f8f9fa; border-radius: 8px;">
            <h3 style="color: #b1030d; margin-bottom: 15px;">系统特色功能说明</h3>
            <div style="margin-bottom: 15px;">
                <strong>1. 错峰预约机制：</strong>按学号尾号分批开放预约权限，有效避免高并发场景，确保系统稳定运行。
            </div>
            <div style="margin-bottom: 15px;">
                <strong>2. 散座机制：</strong>早退或违约释放的座位自动转为散座，可被其他用户直接扫码使用，提高座位利用率。
            </div>
            <div style="margin-bottom: 15px;">
                <strong>3. 违约管理：</strong>完善的违约检测和处罚机制，规范用户使用行为，维护预约秩序。
            </div>
            <div>
                <strong>4. 状态实时同步：</strong>座位状态、预约状态实时更新，确保信息准确性和用户体验。
            </div>
        </div>
    </div>
</body>
</html>
