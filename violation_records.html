<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>违约记录 - 图书馆座位预约系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            -webkit-tap-highlight-color: transparent;
            font-family: "PingFang SC", "Helvetica Neue", Arial, sans-serif;
        }

        body {
            background-color: #f8f8f8;
            color: #333;
            font-size: 14px;
            line-height: 1.5;
            overflow-x: hidden;
        }

        /* 模拟iPhone 15 Pro Max */
        .device-container {
            width: 100%;
            max-width: 430px;
            height: 932px;
            margin: 20px auto;
            position: relative;
            overflow: hidden;
            border-radius: 55px;
            box-shadow: 0 20px 50px rgba(0, 0, 0, 0.3);
            background: #1a1a1a;
            padding: 12px;
        }

        /* 刘海区域 */
        .notch {
            position: absolute;
            width: 160px;
            height: 34px;
            background: #1a1a1a;
            top: 12px;
            left: 50%;
            transform: translateX(-50%);
            border-radius: 0 0 20px 20px;
            z-index: 100;
        }

        /* 底部指示条 */
        .home-indicator {
            position: absolute;
            width: 140px;
            height: 5px;
            background: #ffffff;
            bottom: 25px;
            left: 50%;
            transform: translateX(-50%);
            border-radius: 3px;
            z-index: 100;
        }

        /* 手机屏幕 */
        .screen {
            width: 100%;
            height: 100%;
            background: #ffffff;
            border-radius: 45px;
            overflow: hidden;
            position: relative;
        }

        /* 小程序内容区域 */
        .app-container {
            width: 100%;
            height: 100%;
            overflow: hidden;
            display: flex;
            flex-direction: column;
        }

        /* 顶部导航栏 */
        .nav-bar {
            height: 88px;
            background-color: #b1030d;
            color: white;
            display: flex;
            justify-content: space-between;
            align-items: flex-end;
            padding: 0 16px 10px;
            position: relative;
            z-index: 10;
        }

        .nav-left {
            font-size: 24px;
            padding-bottom: 10px;
        }

        .nav-title {
            font-size: 18px;
            font-weight: 600;
            padding-bottom: 10px;
        }

        .nav-right {
            width: 24px;
            padding-bottom: 10px;
        }

        /* 主内容区 */
        .main-content {
            flex: 1;
            overflow-y: auto;
            -webkit-overflow-scrolling: touch;
            padding: 16px;
            padding-bottom: 70px;
        }

        /* 违约状态卡片 */
        .violation-status-card {
            background: white;
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            animation: fadeIn 0.5s ease forwards;
        }

        .status-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }

        .status-title {
            font-size: 16px;
            font-weight: 600;
            color: #333;
        }

        .status-badge {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 12px;
            font-size: 13px;
            font-weight: 500;
        }

        .status-normal {
            background: #e8f5e9;
            color: #4CAF50;
        }

        .status-warning {
            background: #fff8e1;
            color: #FFC107;
        }

        .status-restricted {
            background: #ffebee;
            color: #f44336;
        }

        .status-content {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 10px 0;
        }

        .status-info {
            display: flex;
            flex-direction: column;
        }

        .status-value {
            font-size: 24px;
            font-weight: 600;
            color: #333;
            margin-bottom: 4px;
        }

        .status-label {
            font-size: 13px;
            color: #999;
        }

        .status-divider {
            width: 1px;
            height: 40px;
            background: #eee;
        }

        .status-message {
            font-size: 13px;
            color: #666;
            background: #f9f9f9;
            padding: 10px;
            border-radius: 8px;
            margin-top: 10px;
            line-height: 1.6;
        }

        /* 违约记录列表 */
        .violation-list-card {
            background: white;
            border-radius: 16px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            overflow: hidden;
            animation: fadeIn 0.5s ease forwards;
            animation-delay: 0.1s;
            opacity: 0;
        }

        .list-header {
            padding: 16px 20px;
            border-bottom: 1px solid #f0f0f0;
        }

        .list-title {
            font-size: 16px;
            font-weight: 600;
            color: #333;
        }

        .violation-item {
            padding: 16px 20px;
            border-bottom: 1px solid #f0f0f0;
            display: flex;
            align-items: flex-start;
        }

        .violation-icon {
            width: 40px;
            height: 40px;
            border-radius: 20px;
            background: #ffebee;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 12px;
            flex-shrink: 0;
        }

        .violation-icon i {
            color: #f44336;
            font-size: 20px;
        }

        .violation-content {
            flex: 1;
        }

        .violation-title {
            font-size: 15px;
            font-weight: 500;
            color: #333;
            margin-bottom: 4px;
        }

        .violation-info {
            font-size: 13px;
            color: #666;
            margin-bottom: 8px;
        }

        .violation-time {
            font-size: 12px;
            color: #999;
        }

        .violation-tag {
            display: inline-block;
            padding: 2px 8px;
            border-radius: 10px;
            font-size: 12px;
            background: #f5f5f5;
            color: #666;
            margin-left: 8px;
        }

        /* 违约规则卡片 */
        .rules-card {
            background: white;
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            animation: fadeIn 0.5s ease forwards;
            animation-delay: 0.2s;
            opacity: 0;
        }

        .rules-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }

        .rules-title {
            font-size: 16px;
            font-weight: 600;
            color: #333;
        }

        .rules-toggle {
            color: #b1030d;
            font-size: 13px;
            display: flex;
            align-items: center;
        }

        .rules-toggle i {
            margin-left: 4px;
            transition: transform 0.3s ease;
        }

        .rules-toggle.expanded i {
            transform: rotate(180deg);
        }

        .rules-content {
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease;
        }

        .rules-content.expanded {
            max-height: 500px;
        }

        .rule-item {
            display: flex;
            margin-bottom: 12px;
        }

        .rule-number {
            width: 20px;
            height: 20px;
            border-radius: 10px;
            background: #f5f5f5;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            color: #666;
            margin-right: 8px;
            flex-shrink: 0;
        }

        .rule-text {
            font-size: 13px;
            color: #666;
            flex: 1;
        }

        /* 空状态 */
        .empty-state {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 40px 20px;
            text-align: center;
            animation: fadeIn 0.5s ease forwards;
            animation-delay: 0.1s;
            opacity: 0;
            display: none;
        }

        .empty-icon {
            width: 80px;
            height: 80px;
            margin-bottom: 16px;
            opacity: 0.5;
        }

        .empty-text {
            font-size: 15px;
            color: #999;
            margin-bottom: 8px;
        }

        .empty-subtext {
            font-size: 13px;
            color: #bbb;
        }

        /* 底部导航 */
        .tab-bar {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: white;
            display: flex;
            box-shadow: 0 -1px 10px rgba(0, 0, 0, 0.05);
            z-index: 100;
        }

        .tab-item {
            flex: 1;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            color: #999;
            font-size: 10px;
            transition: all 0.3s ease;
        }

        .tab-item.active {
            color: #b1030d;
        }

        .tab-icon {
            font-size: 24px;
            margin-bottom: 2px;
        }

        /* 图标字体 */
        .icon {
            font-family: "Material Icons";
            font-weight: normal;
            font-style: normal;
            font-size: 24px;
            display: inline-block;
            line-height: 1;
            text-transform: none;
            letter-spacing: normal;
            word-wrap: normal;
            white-space: nowrap;
            direction: ltr;
            -webkit-font-smoothing: antialiased;
            text-rendering: optimizeLegibility;
            -moz-osx-font-smoothing: grayscale;
            font-feature-settings: 'liga';
        }

        /* 动画效果 */
        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* 下拉刷新 */
        .pull-to-refresh {
            height: 60px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #999;
            font-size: 14px;
            transform: translateY(-60px);
        }

        .refresh-icon {
            margin-right: 8px;
            animation: rotate 1s linear infinite;
        }

        @keyframes rotate {
            from {
                transform: rotate(0deg);
            }
            to {
                transform: rotate(360deg);
            }
        }
    </style>
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
</head>
<body>
    <div class="device-container">
        <div class="notch"></div>
        <div class="home-indicator"></div>
        <div class="screen">
            <div class="app-container">
                <!-- 顶部导航栏 -->
                <div class="nav-bar">
                    <div class="nav-left">
                        <i class="icon" onclick="goBack()">arrow_back</i>
                    </div>
                    <div class="nav-title">违约记录</div>
                    <div class="nav-right"></div>
                </div>

                <!-- 主内容区 -->
                <div class="main-content">
                    <!-- 下拉刷新提示 -->
                    <div class="pull-to-refresh">
                        <i class="icon refresh-icon">refresh</i> 下拉刷新
                    </div>

                    <!-- 违约状态卡片 -->
                    <div class="violation-status-card">
                        <div class="status-header">
                            <div class="status-title">违约状态</div>
                            <div class="status-badge status-warning" id="statusBadge">警告</div>
                        </div>
                        <div class="status-content">
                            <div class="status-info">
                                <div class="status-value">2</div>
                                <div class="status-label">本月违约次数</div>
                            </div>
                            <div class="status-divider"></div>
                            <div class="status-info">
                                <div class="status-value">3</div>
                                <div class="status-label">累计违约次数</div>
                            </div>
                            <div class="status-divider"></div>
                            <div class="status-info">
                                <div class="status-value">1</div>
                                <div class="status-label">剩余警告次数</div>
                            </div>
                        </div>
                        <div class="status-message">
                            注意：本月累计3次违约将暂停预约权限7天，累计5次违约将暂停预约权限14天，累计7次违约将暂停预约权限30天。
                        </div>
                    </div>

                    <!-- 违约记录列表 -->
                    <div class="violation-list-card">
                        <div class="list-header">
                            <div class="list-title">违约记录</div>
                        </div>
                        <div class="violation-item">
                            <div class="violation-icon">
                                <i class="icon">schedule</i>
                            </div>
                            <div class="violation-content">
                                <div class="violation-title">预约未签到</div>
                                <div class="violation-info">A101自习室 A5座位 (12:00-17:00)</div>
                                <div class="violation-time">
                                    2023年5月15日 12:15
                                    <span class="violation-tag">未签到</span>
                                </div>
                            </div>
                        </div>
                        <div class="violation-item">
                            <div class="violation-icon">
                                <i class="icon">timer_off</i>
                            </div>
                            <div class="violation-content">
                                <div class="violation-title">暂离超时</div>
                                <div class="violation-info">B201安静自习室 C8座位 (07:00-12:00)</div>
                                <div class="violation-time">
                                    2023年5月10日 09:45
                                    <span class="violation-tag">暂离超时</span>
                                </div>
                            </div>
                        </div>
                        <div class="violation-item">
                            <div class="violation-icon">
                                <i class="icon">schedule</i>
                            </div>
                            <div class="violation-content">
                                <div class="violation-title">预约未签到</div>
                                <div class="violation-info">C301图书阅览室 D12座位 (17:00-22:00)</div>
                                <div class="violation-time">
                                    2023年4月28日 17:15
                                    <span class="violation-tag">未签到</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 违约规则卡片 -->
                    <div class="rules-card">
                        <div class="rules-header">
                            <div class="rules-title">违约规则说明</div>
                            <div class="rules-toggle" id="rulesToggle" onclick="toggleRules()">
                                查看详情 <i class="icon" style="font-size: 18px;">keyboard_arrow_down</i>
                            </div>
                        </div>
                        <div class="rules-content" id="rulesContent">
                            <div class="rule-item">
                                <div class="rule-number">1</div>
                                <div class="rule-text">预约后未在15分钟内签到，系统将自动取消预约并记录一次违约。</div>
                            </div>
                            <div class="rule-item">
                                <div class="rule-number">2</div>
                                <div class="rule-text">暂离时间超过30分钟未返回，系统将自动释放座位并记录一次违约。</div>
                            </div>
                            <div class="rule-item">
                                <div class="rule-number">3</div>
                                <div class="rule-text">一个月内累计3次违约，将暂停预约权限7天。</div>
                            </div>
                            <div class="rule-item">
                                <div class="rule-number">4</div>
                                <div class="rule-text">一个月内累计5次违约，将暂停预约权限14天。</div>
                            </div>
                            <div class="rule-item">
                                <div class="rule-number">5</div>
                                <div class="rule-text">一个月内累计7次违约，将暂停预约权限30天。</div>
                            </div>
                            <div class="rule-item">
                                <div class="rule-number">6</div>
                                <div class="rule-text">违约记录将在次月1日自动清零，但累计违约记录将被永久保存。</div>
                            </div>
                            <div class="rule-item">
                                <div class="rule-number">7</div>
                                <div class="rule-text">如有特殊情况导致违约，可联系图书馆工作人员申请撤销违约记录。</div>
                            </div>
                        </div>
                    </div>

                    <!-- 空状态 -->
                    <div class="empty-state" id="emptyState">
                        <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iODAiIGhlaWdodD0iODAiIHZpZXdCb3g9IjAgMCA4MCA4MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTQwIDhDMjIuNCAxMCA4IDI0LjQgOCA0MkM4IDU5LjYgMjIuNCA3NCA0MCA3NEM1Ny42IDc0IDcyIDU5LjYgNzIgNDJDNzIgMjQuNCA1Ny42IDEwIDQwIDhaIiBzdHJva2U9IiNDQ0NDQ0MiIHN0cm9rZS13aWR0aD0iNCIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIi8+CjxwYXRoIGQ9Ik00MCAyNkM0Mi4yMDkxIDI2IDQ0IDI0LjIwOTEgNDQgMjJDNDQgMTkuNzkwOSA0Mi4yMDkxIDE4IDQwIDE4QzM3Ljc5MDkgMTggMzYgMTkuNzkwOSAzNiAyMkMzNiAyNC4yMDkxIDM3Ljc5MDkgMjYgNDAgMjZaIiBmaWxsPSIjQ0NDQ0NDIi8+CjxwYXRoIGQ9Ik00MCA2MlY0MCIgc3Ryb2tlPSIjQ0NDQ0NDIiBzdHJva2Utd2lkdGg9IjQiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCIvPgo8L3N2Zz4K" alt="Empty" class="empty-icon">
                        <div class="empty-text">暂无违约记录</div>
                        <div class="empty-subtext">请继续保持良好的预约使用习惯</div>
                    </div>
                </div>

                <!-- 底部导航栏 -->
                <div class="tab-bar">
                    <div class="tab-item">
                        <i class="icon tab-icon">event_seat</i>
                        <div>预约</div>
                    </div>
                    <div class="tab-item">
                        <i class="icon tab-icon">schedule</i>
                        <div>我的座位</div>
                    </div>
                    <div class="tab-item active">
                        <i class="icon tab-icon">person</i>
                        <div>我的</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 切换违约规则展开/收起
        function toggleRules() {
            const rulesToggle = document.getElementById('rulesToggle');
            const rulesContent = document.getElementById('rulesContent');
            
            rulesToggle.classList.toggle('expanded');
            rulesContent.classList.toggle('expanded');
            
            if (rulesToggle.classList.contains('expanded')) {
                rulesToggle.innerHTML = '收起 <i class="icon" style="font-size: 18px;">keyboard_arrow_up</i>';
            } else {
                rulesToggle.innerHTML = '查看详情 <i class="icon" style="font-size: 18px;">keyboard_arrow_down</i>';
            }
        }
        
        // 更新违约状态
        function updateViolationStatus(count) {
            const statusBadge = document.getElementById('statusBadge');
            
            if (count === 0) {
                statusBadge.className = 'status-badge status-normal';
                statusBadge.textContent = '正常';
            } else if (count >= 1 && count < 3) {
                statusBadge.className = 'status-badge status-warning';
                statusBadge.textContent = '警告';
            } else {
                statusBadge.className = 'status-badge status-restricted';
                statusBadge.textContent = '受限';
            }
        }
        
        // 显示/隐藏空状态
        function toggleEmptyState(isEmpty) {
            const emptyState = document.getElementById('emptyState');
            const violationListCard = document.querySelector('.violation-list-card');
            
            if (isEmpty) {
                emptyState.style.display = 'flex';
                violationListCard.style.display = 'none';
            } else {
                emptyState.style.display = 'none';
                violationListCard.style.display = 'block';
            }
        }
        
        // 返回上一页
        function goBack() {
            // 实际应用中应该使用微信小程序的导航API
            alert('返回上一页');
            // wx.navigateBack();
        }
        
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化违约状态
            updateViolationStatus(2);
            
            // 显示违约记录（如果有违约记录则显示列表，否则显示空状态）
            toggleEmptyState(false);
        });
    </script>
</body>
</html>
