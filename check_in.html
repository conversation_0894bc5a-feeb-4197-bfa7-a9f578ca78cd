<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>签到页面 - 图书馆座位预约</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            -webkit-tap-highlight-color: transparent;
            font-family: "PingFang SC", "Helvetica Neue", Arial, sans-serif;
        }

        body {
            background-color: #f8f8f8;
            color: #333;
            font-size: 14px;
            line-height: 1.5;
            overflow-x: hidden;
        }

        /* 模拟iPhone 15 Pro Max */
        .device-container {
            width: 100%;
            max-width: 430px;
            height: 932px;
            margin: 20px auto;
            position: relative;
            overflow: hidden;
            border-radius: 55px;
            box-shadow: 0 20px 50px rgba(0, 0, 0, 0.3);
            background: #1a1a1a;
            padding: 12px;
        }

        /* 刘海区域 */
        .notch {
            position: absolute;
            width: 160px;
            height: 34px;
            background: #1a1a1a;
            top: 12px;
            left: 50%;
            transform: translateX(-50%);
            border-radius: 0 0 20px 20px;
            z-index: 100;
        }

        /* 底部指示条 */
        .home-indicator {
            position: absolute;
            width: 140px;
            height: 5px;
            background: #ffffff;
            bottom: 25px;
            left: 50%;
            transform: translateX(-50%);
            border-radius: 3px;
            z-index: 100;
        }

        /* 手机屏幕 */
        .screen {
            width: 100%;
            height: 100%;
            background: #ffffff;
            border-radius: 45px;
            overflow: hidden;
            position: relative;
        }

        /* 小程序内容区域 */
        .app-container {
            width: 100%;
            height: 100%;
            overflow: hidden;
            display: flex;
            flex-direction: column;
        }

        /* 顶部导航栏 */
        .nav-bar {
            height: 88px;
            background-color: #b1030d;
            color: white;
            display: flex;
            justify-content: space-between;
            align-items: flex-end;
            padding: 0 16px 10px;
            position: relative;
            z-index: 10;
        }

        .nav-left {
            font-size: 24px;
            padding-bottom: 10px;
        }

        .nav-title {
            font-size: 18px;
            font-weight: 600;
            padding-bottom: 10px;
        }

        .nav-right {
            width: 24px;
            padding-bottom: 10px;
        }

        /* 主内容区 */
        .main-content {
            flex: 1;
            overflow-y: auto;
            -webkit-overflow-scrolling: touch;
            padding: 16px;
            padding-bottom: 70px;
        }

        /* 预约信息卡片 */
        .booking-card {
            background: white;
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
        }

        .booking-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }

        .booking-title {
            font-size: 18px;
            font-weight: 600;
            color: #333;
        }

        .booking-id {
            font-size: 13px;
            color: #999;
        }

        .booking-info {
            display: flex;
            flex-wrap: wrap;
        }

        .booking-info-item {
            width: 50%;
            margin-bottom: 12px;
        }

        .info-label {
            font-size: 13px;
            color: #999;
            margin-bottom: 4px;
        }

        .info-value {
            font-size: 15px;
            color: #333;
            font-weight: 500;
        }

        /* 签到状态 */
        .check-in-status {
            background: white;
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            text-align: center;
        }

        .status-icon {
            width: 60px;
            height: 60px;
            border-radius: 30px;
            background: #f5f5f5;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 12px;
            font-size: 30px;
            color: #999;
        }

        .status-icon.checked-in {
            background: #e8f5e9;
            color: #4CAF50;
        }

        .status-icon.temp-leave {
            background: #fff8e1;
            color: #FFC107;
        }

        .status-text {
            font-size: 18px;
            font-weight: 600;
            color: #333;
            margin-bottom: 8px;
        }

        .status-desc {
            font-size: 14px;
            color: #666;
            margin-bottom: 16px;
        }

        /* 倒计时 */
        .countdown {
            display: flex;
            justify-content: center;
            align-items: center;
            margin-bottom: 16px;
        }

        .countdown-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            margin: 0 8px;
        }

        .countdown-value {
            width: 40px;
            height: 40px;
            background: #f5f5f5;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
            font-weight: 600;
            color: #333;
            margin-bottom: 4px;
        }

        .countdown-label {
            font-size: 12px;
            color: #999;
        }

        .countdown-separator {
            font-size: 20px;
            font-weight: 600;
            color: #333;
            margin-top: -20px;
        }

        /* 扫码签到按钮 */
        .scan-button {
            background: white;
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            display: flex;
            flex-direction: column;
            align-items: center;
        }

        .scan-icon {
            width: 80px;
            height: 80px;
            border-radius: 40px;
            background: #b1030d;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 16px;
            font-size: 40px;
            color: white;
            box-shadow: 0 4px 12px rgba(177, 3, 13, 0.3);
        }

        .scan-text {
            font-size: 16px;
            font-weight: 600;
            color: #333;
            margin-bottom: 8px;
        }

        .scan-desc {
            font-size: 13px;
            color: #666;
            text-align: center;
        }

        /* 操作按钮 */
        .action-buttons {
            display: flex;
            justify-content: space-between;
            margin-bottom: 20px;
        }

        .action-button {
            flex: 1;
            height: 50px;
            border-radius: 25px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
            font-weight: 600;
            margin: 0 8px;
        }

        .action-button:first-child {
            margin-left: 0;
        }

        .action-button:last-child {
            margin-right: 0;
        }

        .temp-leave-btn {
            background: #fff8e1;
            color: #FFC107;
            border: 1px solid #FFC107;
        }

        .check-out-btn {
            background: #ffebee;
            color: #b1030d;
            border: 1px solid #b1030d;
        }

        /* 底部导航 */
        .tab-bar {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: white;
            display: flex;
            box-shadow: 0 -1px 10px rgba(0, 0, 0, 0.05);
            z-index: 100;
        }

        .tab-item {
            flex: 1;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            color: #999;
            font-size: 10px;
            transition: all 0.3s ease;
        }

        .tab-item.active {
            color: #b1030d;
        }

        .tab-icon {
            font-size: 24px;
            margin-bottom: 2px;
        }

        /* 图标字体 */
        .icon {
            font-family: "Material Icons";
            font-weight: normal;
            font-style: normal;
            font-size: 24px;
            display: inline-block;
            line-height: 1;
            text-transform: none;
            letter-spacing: normal;
            word-wrap: normal;
            white-space: nowrap;
            direction: ltr;
            -webkit-font-smoothing: antialiased;
            text-rendering: optimizeLegibility;
            -moz-osx-font-smoothing: grayscale;
            font-feature-settings: 'liga';
        }

        /* 动画效果 */
        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .animate-in {
            animation: fadeIn 0.5s ease forwards;
        }

        @keyframes pulse {
            0% {
                transform: scale(1);
            }
            50% {
                transform: scale(1.05);
            }
            100% {
                transform: scale(1);
            }
        }

        .pulse {
            animation: pulse 2s infinite;
        }

        /* 模态框 */
        .modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1000;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
        }

        .modal.show {
            opacity: 1;
            visibility: visible;
        }

        .modal-content {
            width: 80%;
            max-width: 320px;
            background: white;
            border-radius: 16px;
            padding: 24px;
            text-align: center;
            transform: translateY(20px);
            transition: all 0.3s ease;
        }

        .modal.show .modal-content {
            transform: translateY(0);
        }

        .modal-icon {
            width: 60px;
            height: 60px;
            border-radius: 30px;
            background: #e8f5e9;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 16px;
            font-size: 30px;
            color: #4CAF50;
        }

        .modal-title {
            font-size: 18px;
            font-weight: 600;
            color: #333;
            margin-bottom: 8px;
        }

        .modal-desc {
            font-size: 14px;
            color: #666;
            margin-bottom: 20px;
        }

        .modal-button {
            height: 44px;
            border-radius: 22px;
            background: #b1030d;
            color: white;
            font-size: 16px;
            font-weight: 600;
            display: flex;
            align-items: center;
            justify-content: center;
        }
    </style>
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
</head>
<body>
    <div class="device-container">
        <div class="notch"></div>
        <div class="home-indicator"></div>
        <div class="screen">
            <div class="app-container">
                <!-- 顶部导航栏 -->
                <div class="nav-bar">
                    <div class="nav-left">
                        <i class="icon" onclick="goBack()">arrow_back</i>
                    </div>
                    <div class="nav-title">签到</div>
                    <div class="nav-right"></div>
                </div>

                <!-- 主内容区 -->
                <div class="main-content">
                    <!-- 预约信息卡片 -->
                    <div class="booking-card animate-in">
                        <div class="booking-header">
                            <div class="booking-title">A101 自习室</div>
                            <div class="booking-id">预约号: 2023051701</div>
                        </div>
                        <div class="booking-info">
                            <div class="booking-info-item">
                                <div class="info-label">座位号</div>
                                <div class="info-value">A5</div>
                            </div>
                            <div class="booking-info-item">
                                <div class="info-label">日期</div>
                                <div class="info-value">2023年5月17日</div>
                            </div>
                            <div class="booking-info-item">
                                <div class="info-label">时间段</div>
                                <div class="info-value">12:00 - 17:00</div>
                            </div>
                            <div class="booking-info-item">
                                <div class="info-label">位置</div>
                                <div class="info-value">图书馆1楼</div>
                            </div>
                        </div>
                    </div>

                    <!-- 签到状态 -->
                    <div class="check-in-status animate-in" style="animation-delay: 0.1s;">
                        <div class="status-icon" id="statusIcon">
                            <i class="icon">schedule</i>
                        </div>
                        <div class="status-text" id="statusText">未签到</div>
                        <div class="status-desc" id="statusDesc">请在12:15前完成签到，否则预约将被取消</div>

                        <!-- 倒计时 -->
                        <div class="countdown" id="countdown">
                            <div class="countdown-item">
                                <div class="countdown-value" id="hoursValue">00</div>
                                <div class="countdown-label">时</div>
                            </div>
                            <div class="countdown-separator">:</div>
                            <div class="countdown-item">
                                <div class="countdown-value" id="minutesValue">15</div>
                                <div class="countdown-label">分</div>
                            </div>
                            <div class="countdown-separator">:</div>
                            <div class="countdown-item">
                                <div class="countdown-value" id="secondsValue">00</div>
                                <div class="countdown-label">秒</div>
                            </div>
                        </div>
                    </div>

                    <!-- 扫码签到按钮 -->
                    <div class="scan-button animate-in" style="animation-delay: 0.2s;" id="scanButton">
                        <div class="scan-icon pulse" onclick="scanQRCode()">
                            <i class="icon">qr_code_scanner</i>
                        </div>
                        <div class="scan-text">扫码签到</div>
                        <div class="scan-desc">请扫描座位上的二维码完成签到</div>
                    </div>

                    <!-- 操作按钮 -->
                    <div class="action-buttons animate-in" style="animation-delay: 0.3s; display: none;" id="actionButtons">
                        <div class="action-button temp-leave-btn" onclick="tempLeave()">
                            <i class="icon" style="margin-right: 4px;">timer</i> 暂离
                        </div>
                        <div class="action-button check-out-btn" onclick="checkOut()">
                            <i class="icon" style="margin-right: 4px;">logout</i> 签退
                        </div>
                    </div>
                </div>

                <!-- 底部导航栏 -->
                <div class="tab-bar">
                    <div class="tab-item">
                        <i class="icon tab-icon">event_seat</i>
                        <div>预约</div>
                    </div>
                    <div class="tab-item active">
                        <i class="icon tab-icon">schedule</i>
                        <div>我的座位</div>
                    </div>
                    <div class="tab-item">
                        <i class="icon tab-icon">person</i>
                        <div>我的</div>
                    </div>
                </div>

                <!-- 签到成功模态框 -->
                <div class="modal" id="successModal">
                    <div class="modal-content">
                        <div class="modal-icon">
                            <i class="icon">check_circle</i>
                        </div>
                        <div class="modal-title">签到成功</div>
                        <div class="modal-desc">您已成功签到A101自习室A5座位<br>请在17:00前使用完毕</div>
                        <div class="modal-button" onclick="closeModal()">
                            确定
                        </div>
                    </div>
                </div>

                <!-- 暂离确认模态框 -->
                <div class="modal" id="tempLeaveModal">
                    <div class="modal-content">
                        <div class="modal-icon" style="background: #fff8e1; color: #FFC107;">
                            <i class="icon">timer</i>
                        </div>
                        <div class="modal-title">确认暂离</div>
                        <div class="modal-desc">暂离时间不得超过30分钟<br>超时未返回座位将被释放</div>
                        <div class="modal-button" onclick="confirmTempLeave()">
                            确认暂离
                        </div>
                    </div>
                </div>

                <!-- 签退确认模态框 -->
                <div class="modal" id="checkOutModal">
                    <div class="modal-content">
                        <div class="modal-icon" style="background: #ffebee; color: #b1030d;">
                            <i class="icon">logout</i>
                        </div>
                        <div class="modal-title">确认签退</div>
                        <div class="modal-desc">签退后座位将被释放<br>确定要结束本次使用吗？</div>
                        <div class="modal-button" onclick="confirmCheckOut()">
                            确认签退
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 当前状态
        let currentStatus = 'not_checked_in'; // not_checked_in, checked_in, temp_leave
        let countdownInterval;

        // 扫码签到
        function scanQRCode() {
            // 模拟扫码过程
            setTimeout(() => {
                // 显示签到成功模态框
                document.getElementById('successModal').classList.add('show');
                
                // 更新状态为已签到
                updateStatus('checked_in');
                
                // 隐藏扫码按钮，显示操作按钮
                document.getElementById('scanButton').style.display = 'none';
                document.getElementById('actionButtons').style.display = 'flex';
                
                // 开始倒计时
                startCountdown(5, 0, 0); // 5小时倒计时
            }, 1000);
        }

        // 暂离
        function tempLeave() {
            document.getElementById('tempLeaveModal').classList.add('show');
        }

        // 确认暂离
        function confirmTempLeave() {
            // 关闭模态框
            document.getElementById('tempLeaveModal').classList.remove('show');
            
            // 更新状态为暂离
            updateStatus('temp_leave');
            
            // 开始暂离倒计时
            startCountdown(0, 30, 0); // 30分钟倒计时
        }

        // 签退
        function checkOut() {
            document.getElementById('checkOutModal').classList.add('show');
        }

        // 确认签退
        function confirmCheckOut() {
            // 关闭模态框
            document.getElementById('checkOutModal').classList.remove('show');
            
            // 模拟返回上一页
            alert('签退成功，即将返回预约列表');
            // 实际应用中应该使用微信小程序的导航API
            // wx.navigateBack();
        }

        // 关闭模态框
        function closeModal() {
            document.getElementById('successModal').classList.remove('show');
            document.getElementById('tempLeaveModal').classList.remove('show');
            document.getElementById('checkOutModal').classList.remove('show');
        }

        // 更新状态
        function updateStatus(status) {
            currentStatus = status;
            const statusIcon = document.getElementById('statusIcon');
            const statusText = document.getElementById('statusText');
            const statusDesc = document.getElementById('statusDesc');
            
            // 清除之前的类
            statusIcon.className = 'status-icon';
            
            if (status === 'checked_in') {
                statusIcon.classList.add('checked-in');
                statusIcon.innerHTML = '<i class="icon">check_circle</i>';
                statusText.textContent = '已签到';
                statusDesc.textContent = '请在规定时间内使用座位';
            } else if (status === 'temp_leave') {
                statusIcon.classList.add('temp-leave');
                statusIcon.innerHTML = '<i class="icon">timer</i>';
                statusText.textContent = '暂离中';
                statusDesc.textContent = '请在30分钟内返回，否则座位将被释放';
            } else {
                statusIcon.innerHTML = '<i class="icon">schedule</i>';
                statusText.textContent = '未签到';
                statusDesc.textContent = '请在12:15前完成签到，否则预约将被取消';
            }
        }

        // 开始倒计时
        function startCountdown(hours, minutes, seconds) {
            // 清除之前的倒计时
            if (countdownInterval) {
                clearInterval(countdownInterval);
            }
            
            // 计算总秒数
            let totalSeconds = hours * 3600 + minutes * 60 + seconds;
            
            // 更新倒计时显示
            updateCountdownDisplay(totalSeconds);
            
            // 设置倒计时间隔
            countdownInterval = setInterval(() => {
                totalSeconds--;
                
                if (totalSeconds <= 0) {
                    clearInterval(countdownInterval);
                    
                    // 如果是暂离状态，时间到了自动释放座位
                    if (currentStatus === 'temp_leave') {
                        alert('暂离时间已到，座位已被释放');
                        // 实际应用中应该使用微信小程序的导航API
                        // wx.navigateBack();
                    }
                }
                
                updateCountdownDisplay(totalSeconds);
            }, 1000);
        }

        // 更新倒计时显示
        function updateCountdownDisplay(totalSeconds) {
            const hours = Math.floor(totalSeconds / 3600);
            const minutes = Math.floor((totalSeconds % 3600) / 60);
            const seconds = totalSeconds % 60;
            
            document.getElementById('hoursValue').textContent = hours.toString().padStart(2, '0');
            document.getElementById('minutesValue').textContent = minutes.toString().padStart(2, '0');
            document.getElementById('secondsValue').textContent = seconds.toString().padStart(2, '0');
            
            // 如果倒计时小于5分钟，添加警示效果
            if (totalSeconds < 300) {
                document.getElementById('countdown').style.color = '#b1030d';
            } else {
                document.getElementById('countdown').style.color = '';
            }
        }

        // 返回上一页
        function goBack() {
            // 实际应用中应该使用微信小程序的导航API
            alert('返回上一页');
            // wx.navigateBack();
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化倒计时
            startCountdown(0, 15, 0); // 15分钟签到倒计时
        });
    </script>
</body>
</html>
