<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>我的预约 - 图书馆座位预约</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            -webkit-tap-highlight-color: transparent;
            font-family: "PingFang SC", "Helvetica Neue", Arial, sans-serif;
        }

        body {
            background-color: #f8f8f8;
            color: #333;
            font-size: 14px;
            line-height: 1.5;
            overflow-x: hidden;
        }

        /* 模拟iPhone 15 Pro Max */
        .device-container {
            width: 100%;
            max-width: 430px;
            height: 932px;
            margin: 20px auto;
            position: relative;
            overflow: hidden;
            border-radius: 55px;
            box-shadow: 0 20px 50px rgba(0, 0, 0, 0.3);
            background: #1a1a1a;
            padding: 12px;
        }

        /* 刘海区域 */
        .notch {
            position: absolute;
            width: 160px;
            height: 34px;
            background: #1a1a1a;
            top: 12px;
            left: 50%;
            transform: translateX(-50%);
            border-radius: 0 0 20px 20px;
            z-index: 100;
        }

        /* 底部指示条 */
        .home-indicator {
            position: absolute;
            width: 140px;
            height: 5px;
            background: #ffffff;
            bottom: 25px;
            left: 50%;
            transform: translateX(-50%);
            border-radius: 3px;
            z-index: 100;
        }

        /* 手机屏幕 */
        .screen {
            width: 100%;
            height: 100%;
            background: #ffffff;
            border-radius: 45px;
            overflow: hidden;
            position: relative;
        }

        /* 小程序内容区域 */
        .app-container {
            width: 100%;
            height: 100%;
            overflow: hidden;
            display: flex;
            flex-direction: column;
        }

        /* 顶部导航栏 */
        .nav-bar {
            height: 88px;
            background-color: #b1030d;
            color: white;
            display: flex;
            justify-content: space-between;
            align-items: flex-end;
            padding: 0 16px 10px;
            position: relative;
            z-index: 10;
        }

        .nav-left {
            width: 24px;
            padding-bottom: 10px;
        }

        .nav-title {
            font-size: 18px;
            font-weight: 600;
            padding-bottom: 10px;
        }

        .nav-right {
            width: 24px;
            padding-bottom: 10px;
        }

        /* 分类标签栏 */
        .tab-header {
            height: 44px;
            background: white;
            display: flex;
            border-bottom: 1px solid #f0f0f0;
            position: sticky;
            top: 0;
            z-index: 10;
        }

        .tab-item-header {
            flex: 1;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 15px;
            color: #666;
            position: relative;
        }

        .tab-item-header.active {
            color: #b1030d;
            font-weight: 600;
        }

        .tab-indicator {
            position: absolute;
            bottom: 0;
            left: 25%;
            width: 50%;
            height: 3px;
            background: #b1030d;
            border-radius: 1.5px;
        }

        /* 主内容区 */
        .main-content {
            flex: 1;
            overflow-y: auto;
            -webkit-overflow-scrolling: touch;
            padding: 16px;
            padding-bottom: 70px;
        }

        /* 预约卡片 */
        .booking-card {
            background: white;
            border-radius: 16px;
            margin-bottom: 16px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            overflow: hidden;
            animation: fadeIn 0.5s ease forwards;
        }

        .booking-card-header {
            padding: 16px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 1px solid #f5f5f5;
        }

        .booking-room {
            font-size: 16px;
            font-weight: 600;
            color: #333;
        }

        .booking-status {
            font-size: 13px;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }

        .status-waiting {
            background: #e3f2fd;
            color: #2196F3;
        }

        .status-using {
            background: #e8f5e9;
            color: #4CAF50;
        }

        .status-temp-leave {
            background: #fff8e1;
            color: #FFC107;
        }

        .status-completed {
            background: #f5f5f5;
            color: #9e9e9e;
        }

        .booking-card-content {
            padding: 16px;
        }

        .booking-info-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 12px;
        }

        .booking-info-item {
            display: flex;
            flex-direction: column;
        }

        .info-label {
            font-size: 13px;
            color: #999;
            margin-bottom: 4px;
        }

        .info-value {
            font-size: 15px;
            color: #333;
        }

        .booking-card-actions {
            padding: 12px 16px;
            display: flex;
            justify-content: flex-end;
            border-top: 1px solid #f5f5f5;
        }

        .card-action-btn {
            height: 36px;
            padding: 0 16px;
            border-radius: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            font-weight: 500;
            margin-left: 12px;
        }

        .primary-btn {
            background: #b1030d;
            color: white;
        }

        .secondary-btn {
            background: white;
            color: #666;
            border: 1px solid #ddd;
        }

        /* 空状态 */
        .empty-state {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 60px 20px;
            text-align: center;
        }

        .empty-icon {
            width: 100px;
            height: 100px;
            margin-bottom: 20px;
            opacity: 0.5;
        }

        .empty-text {
            font-size: 16px;
            color: #999;
            margin-bottom: 20px;
        }

        .empty-btn {
            height: 40px;
            padding: 0 20px;
            border-radius: 20px;
            background: #b1030d;
            color: white;
            font-size: 14px;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        /* 底部导航 */
        .tab-bar {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: white;
            display: flex;
            box-shadow: 0 -1px 10px rgba(0, 0, 0, 0.05);
            z-index: 100;
        }

        .tab-item {
            flex: 1;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            color: #999;
            font-size: 10px;
            transition: all 0.3s ease;
        }

        .tab-item.active {
            color: #b1030d;
        }

        .tab-icon {
            font-size: 24px;
            margin-bottom: 2px;
        }

        /* 图标字体 */
        .icon {
            font-family: "Material Icons";
            font-weight: normal;
            font-style: normal;
            font-size: 24px;
            display: inline-block;
            line-height: 1;
            text-transform: none;
            letter-spacing: normal;
            word-wrap: normal;
            white-space: nowrap;
            direction: ltr;
            -webkit-font-smoothing: antialiased;
            text-rendering: optimizeLegibility;
            -moz-osx-font-smoothing: grayscale;
            font-feature-settings: 'liga';
        }

        /* 动画效果 */
        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* 下拉刷新 */
        .pull-to-refresh {
            height: 60px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #999;
            font-size: 14px;
            transform: translateY(-60px);
        }

        .refresh-icon {
            margin-right: 8px;
            animation: rotate 1s linear infinite;
        }

        @keyframes rotate {
            from {
                transform: rotate(0deg);
            }
            to {
                transform: rotate(360deg);
            }
        }
    </style>
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
</head>
<body>
    <div class="device-container">
        <div class="notch"></div>
        <div class="home-indicator"></div>
        <div class="screen">
            <div class="app-container">
                <!-- 顶部导航栏 -->
                <div class="nav-bar">
                    <div class="nav-left"></div>
                    <div class="nav-title">我的预约</div>
                    <div class="nav-right"></div>
                </div>

                <!-- 分类标签栏 -->
                <div class="tab-header">
                    <div class="tab-item-header active" onclick="switchTab('current')">
                        当前预约
                        <div class="tab-indicator"></div>
                    </div>
                    <div class="tab-item-header" onclick="switchTab('history')">
                        历史预约
                    </div>
                </div>

                <!-- 主内容区 - 当前预约 -->
                <div class="main-content" id="currentTab">
                    <!-- 下拉刷新提示 -->
                    <div class="pull-to-refresh">
                        <i class="icon refresh-icon">refresh</i> 下拉刷新
                    </div>

                    <!-- 预约卡片 - 使用中 -->
                    <div class="booking-card" style="animation-delay: 0.1s;">
                        <div class="booking-card-header">
                            <div class="booking-room">A101 自习室</div>
                            <div class="booking-status status-using">使用中</div>
                        </div>
                        <div class="booking-card-content">
                            <div class="booking-info-grid">
                                <div class="booking-info-item">
                                    <div class="info-label">座位号</div>
                                    <div class="info-value">A5</div>
                                </div>
                                <div class="booking-info-item">
                                    <div class="info-label">日期</div>
                                    <div class="info-value">2023年5月17日</div>
                                </div>
                                <div class="booking-info-item">
                                    <div class="info-label">时间段</div>
                                    <div class="info-value">12:00 - 17:00</div>
                                </div>
                                <div class="booking-info-item">
                                    <div class="info-label">剩余时间</div>
                                    <div class="info-value">3小时45分钟</div>
                                </div>
                            </div>
                        </div>
                        <div class="booking-card-actions">
                            <div class="card-action-btn secondary-btn" onclick="tempLeave('A101', 'A5')">暂离</div>
                            <div class="card-action-btn primary-btn" onclick="viewSeat('A101', 'A5')">查看座位</div>
                        </div>
                    </div>

                    <!-- 预约卡片 - 待签到 -->
                    <div class="booking-card" style="animation-delay: 0.2s;">
                        <div class="booking-card-header">
                            <div class="booking-room">B201 安静自习室</div>
                            <div class="booking-status status-waiting">待签到</div>
                        </div>
                        <div class="booking-card-content">
                            <div class="booking-info-grid">
                                <div class="booking-info-item">
                                    <div class="info-label">座位号</div>
                                    <div class="info-value">C8</div>
                                </div>
                                <div class="booking-info-item">
                                    <div class="info-label">日期</div>
                                    <div class="info-value">2023年5月18日</div>
                                </div>
                                <div class="booking-info-item">
                                    <div class="info-label">时间段</div>
                                    <div class="info-value">07:00 - 12:00</div>
                                </div>
                                <div class="booking-info-item">
                                    <div class="info-label">签到时间</div>
                                    <div class="info-value">07:15前</div>
                                </div>
                            </div>
                        </div>
                        <div class="booking-card-actions">
                            <div class="card-action-btn secondary-btn" onclick="cancelBooking('B201', 'C8')">取消预约</div>
                            <div class="card-action-btn primary-btn" onclick="checkIn('B201', 'C8')">签到</div>
                        </div>
                    </div>

                    <!-- 预约卡片 - 暂离中 -->
                    <div class="booking-card" style="animation-delay: 0.3s;">
                        <div class="booking-card-header">
                            <div class="booking-room">C301 图书阅览室</div>
                            <div class="booking-status status-temp-leave">暂离中</div>
                        </div>
                        <div class="booking-card-content">
                            <div class="booking-info-grid">
                                <div class="booking-info-item">
                                    <div class="info-label">座位号</div>
                                    <div class="info-value">D12</div>
                                </div>
                                <div class="booking-info-item">
                                    <div class="info-label">日期</div>
                                    <div class="info-value">2023年5月17日</div>
                                </div>
                                <div class="booking-info-item">
                                    <div class="info-label">时间段</div>
                                    <div class="info-value">17:00 - 22:00</div>
                                </div>
                                <div class="booking-info-item">
                                    <div class="info-label">剩余暂离时间</div>
                                    <div class="info-value">15分钟</div>
                                </div>
                            </div>
                        </div>
                        <div class="booking-card-actions">
                            <div class="card-action-btn secondary-btn" onclick="checkOut('C301', 'D12')">签退</div>
                            <div class="card-action-btn primary-btn" onclick="returnSeat('C301', 'D12')">返回座位</div>
                        </div>
                    </div>
                </div>

                <!-- 主内容区 - 历史预约 -->
                <div class="main-content" id="historyTab" style="display: none;">
                    <!-- 预约卡片 - 已完成 -->
                    <div class="booking-card">
                        <div class="booking-card-header">
                            <div class="booking-room">A102 电子阅览室</div>
                            <div class="booking-status status-completed">已完成</div>
                        </div>
                        <div class="booking-card-content">
                            <div class="booking-info-grid">
                                <div class="booking-info-item">
                                    <div class="info-label">座位号</div>
                                    <div class="info-value">B7</div>
                                </div>
                                <div class="booking-info-item">
                                    <div class="info-label">日期</div>
                                    <div class="info-value">2023年5月15日</div>
                                </div>
                                <div class="booking-info-item">
                                    <div class="info-label">时间段</div>
                                    <div class="info-value">12:00 - 17:00</div>
                                </div>
                                <div class="booking-info-item">
                                    <div class="info-label">使用时长</div>
                                    <div class="info-value">4小时30分钟</div>
                                </div>
                            </div>
                        </div>
                        <div class="booking-card-actions">
                            <div class="card-action-btn secondary-btn" onclick="bookAgain('A102', 'B7')">再次预约</div>
                        </div>
                    </div>

                    <!-- 预约卡片 - 已完成 -->
                    <div class="booking-card">
                        <div class="booking-card-header">
                            <div class="booking-room">B202 研讨室</div>
                            <div class="booking-status status-completed">已完成</div>
                        </div>
                        <div class="booking-card-content">
                            <div class="booking-info-grid">
                                <div class="booking-info-item">
                                    <div class="info-label">座位号</div>
                                    <div class="info-value">E3</div>
                                </div>
                                <div class="booking-info-item">
                                    <div class="info-label">日期</div>
                                    <div class="info-value">2023年5月14日</div>
                                </div>
                                <div class="booking-info-item">
                                    <div class="info-label">时间段</div>
                                    <div class="info-value">07:00 - 12:00</div>
                                </div>
                                <div class="booking-info-item">
                                    <div class="info-label">使用时长</div>
                                    <div class="info-value">5小时</div>
                                </div>
                            </div>
                        </div>
                        <div class="booking-card-actions">
                            <div class="card-action-btn secondary-btn" onclick="bookAgain('B202', 'E3')">再次预约</div>
                        </div>
                    </div>

                    <!-- 预约卡片 - 已取消 -->
                    <div class="booking-card">
                        <div class="booking-card-header">
                            <div class="booking-room">A101 自习室</div>
                            <div class="booking-status status-completed">已取消</div>
                        </div>
                        <div class="booking-card-content">
                            <div class="booking-info-grid">
                                <div class="booking-info-item">
                                    <div class="info-label">座位号</div>
                                    <div class="info-value">A9</div>
                                </div>
                                <div class="booking-info-item">
                                    <div class="info-label">日期</div>
                                    <div class="info-value">2023年5月13日</div>
                                </div>
                                <div class="booking-info-item">
                                    <div class="info-label">时间段</div>
                                    <div class="info-value">17:00 - 22:00</div>
                                </div>
                                <div class="booking-info-item">
                                    <div class="info-label">取消时间</div>
                                    <div class="info-value">16:30</div>
                                </div>
                            </div>
                        </div>
                        <div class="booking-card-actions">
                            <div class="card-action-btn secondary-btn" onclick="bookAgain('A101', 'A9')">再次预约</div>
                        </div>
                    </div>

                    <!-- 空状态（当没有历史预约时显示） -->
                    <div class="empty-state" style="display: none;">
                        <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgdmlld0JveD0iMCAwIDEwMCAxMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxwYXRoIGQ9Ik01MCA4QzI3LjkgOCAxMCAyNS45IDEwIDQ4QzEwIDcwLjEgMjcuOSA4OCA1MCA4OEM3Mi4xIDg4IDkwIDcwLjEgOTAgNDhDOTAgMjUuOSA3Mi4xIDggNTAgOFpNNTAgMTZDNjcuNyAxNiA4MiAzMC4zIDgyIDQ4QzgyIDY1LjcgNjcuNyA4MCA1MCA4MEMzMi4zIDgwIDE4IDY1LjcgMTggNDhDMTggMzAuMyAzMi4zIDE2IDUwIDE2WiIgZmlsbD0iI0NDQ0NDQyIvPgo8cGF0aCBkPSJNNTAgMjRDMzYuNyAyNCAyNiAzNC43IDI2IDQ4QzI2IDYxLjMgMzYuNyA3MiA1MCA3MkM2My4zIDcyIDc0IDYxLjMgNzQgNDhDNzQgMzQuNyA2My4zIDI0IDUwIDI0Wk01MCAzMkM1OC44IDMyIDY2IDM5LjIgNjYgNDhDNjYgNTYuOCA1OC44IDY0IDUwIDY0QzQxLjIgNjQgMzQgNTYuOCAzNCA0OEMzNCAzOS4yIDQxLjIgMzIgNTAgMzJaIiBmaWxsPSIjQ0NDQ0NDIi8+CjxwYXRoIGQ9Ik01MCA0MEMzNi43IDQwIDI2IDQ0IDI2IDQ4QzI2IDUyIDM2LjcgNTYgNTAgNTZDNjMuMyA1NiA3NCA1MiA3NCA0OEM3NCA0NCA2My4zIDQwIDUwIDQwWiIgZmlsbD0iI0NDQ0NDQyIvPgo8L3N2Zz4K" alt="Empty" class="empty-icon">
                        <div class="empty-text">暂无历史预约记录</div>
                        <div class="empty-btn" onclick="goToBooking()">
                            <i class="icon" style="margin-right: 4px; font-size: 18px;">add</i> 立即预约
                        </div>
                    </div>
                </div>

                <!-- 底部导航栏 -->
                <div class="tab-bar">
                    <div class="tab-item">
                        <i class="icon tab-icon">event_seat</i>
                        <div>预约</div>
                    </div>
                    <div class="tab-item active">
                        <i class="icon tab-icon">schedule</i>
                        <div>我的座位</div>
                    </div>
                    <div class="tab-item">
                        <i class="icon tab-icon">person</i>
                        <div>我的</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 切换标签页
        function switchTab(tab) {
            const currentTab = document.getElementById('currentTab');
            const historyTab = document.getElementById('historyTab');
            const tabHeaders = document.querySelectorAll('.tab-item-header');
            
            if (tab === 'current') {
                currentTab.style.display = 'block';
                historyTab.style.display = 'none';
                tabHeaders[0].classList.add('active');
                tabHeaders[1].classList.remove('active');
                tabHeaders[0].innerHTML = '当前预约<div class="tab-indicator"></div>';
                tabHeaders[1].innerHTML = '历史预约';
            } else {
                currentTab.style.display = 'none';
                historyTab.style.display = 'block';
                tabHeaders[0].classList.remove('active');
                tabHeaders[1].classList.add('active');
                tabHeaders[0].innerHTML = '当前预约';
                tabHeaders[1].innerHTML = '历史预约<div class="tab-indicator"></div>';
            }
        }

        // 查看座位
        function viewSeat(room, seat) {
            alert(`查看座位: ${room} ${seat}`);
            // 实际应用中应该跳转到座位使用页面
            // window.location.href = `seat_in_use.html?room=${room}&seat=${seat}`;
        }

        // 暂离
        function tempLeave(room, seat) {
            alert(`暂离座位: ${room} ${seat}`);
            // 实际应用中应该跳转到暂离确认页面
        }

        // 签到
        function checkIn(room, seat) {
            alert(`签到: ${room} ${seat}`);
            // 实际应用中应该跳转到签到页面
            // window.location.href = `check_in.html?room=${room}&seat=${seat}`;
        }

        // 取消预约
        function cancelBooking(room, seat) {
            alert(`取消预约: ${room} ${seat}`);
            // 实际应用中应该弹出确认对话框
        }

        // 返回座位
        function returnSeat(room, seat) {
            alert(`返回座位: ${room} ${seat}`);
            // 实际应用中应该跳转到返回确认页面
        }

        // 签退
        function checkOut(room, seat) {
            alert(`签退: ${room} ${seat}`);
            // 实际应用中应该弹出确认对话框
        }

        // 再次预约
        function bookAgain(room, seat) {
            alert(`再次预约: ${room} ${seat}`);
            // 实际应用中应该跳转到预约页面
            // window.location.href = `booking_main.html?room=${room}`;
        }

        // 前往预约页面
        function goToBooking() {
            alert('前往预约页面');
            // 实际应用中应该跳转到预约主页
            // window.location.href = 'booking_main.html';
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 可以添加一些初始化逻辑
        });
    </script>
</body>
</html>
